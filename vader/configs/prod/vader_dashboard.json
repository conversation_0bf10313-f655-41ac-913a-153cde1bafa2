{"machine": "dashboard", "prod": true, "devicePaths": ["/", "/app"], "services": {"platform": {"name": "Platform", "processPath": "/app/precize-server/precize-dashboard-0.0.1-SNAPSHOT.jar", "processName": "precize-dashboard-0.0.1-SNAPSHOT.jar", "type": "platform", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/servers/dashboard/target/precize-dashboard-0.0.1-SNAPSHOT.jar", "configFiles": ["application.yml"], "monitor": true, "autoRestart": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}, "jumpMachines": {"elastic-master": {"id": "master-node", "name": "Elastic Master Node", "services": {"elastic": {"processPath": "/app/elastic/elasticsearch", "name": "ElasticSearch", "monitor": true}}, "devicePaths": ["/", "/app"], "type": "elastic_master_node", "enabled": true}, "elastic-datanode-1": {"id": "data-node-01", "name": "Elastic Data Node 1", "services": {"elastic": {"processPath": "/app/elastic/elasticsearch", "name": "ElasticSearch", "monitor": true}}, "devicePaths": ["/", "/app"], "type": "elastic_data_node", "enabled": true}, "elastic-datanode-2": {"id": "data-node-02", "name": "Elastic Data Node 2", "services": {"elastic": {"processPath": "/app/elastic/elasticsearch", "name": "ElasticSearch", "monitor": true}}, "devicePaths": ["/", "/app"], "type": "elastic_data_node", "enabled": true}, "redis": {"id": "redis", "name": "Redis", "services": {"redis": {"processPath": "/usr/bin/redis-server", "name": "Redis", "monitor": true}}, "devicePaths": ["/", "/app"], "type": "redis", "enabled": true}}, "files": {"application.yml": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/config/prod/application-dash.yml", "filePath": "/app/precize-server/application.yml"}}}