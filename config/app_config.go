package config

import (
	"os"

	"gopkg.in/yaml.v2"
)

const (
	QA_ENV      = "QA"
	PROD_ENV    = "prod"
	PREPROD_ENV = "preprod"
)

var (
	Environment string
)

type ApplicationConfig struct {
	Environment string `yaml:"env"`
	ScanServer  struct {
		Name    string `yaml:"name"`
		Host    string `yaml:"host"`
		Scheme  string `yaml:"scheme"`
		Port    int    `yaml:"port"`
		Servlet struct {
			ContextPath string `yaml:"context-path"`
		} `yaml:"servlet"`
	} `yaml:"scan-server"`
	Server struct {
		Name string `yaml:"name"`
		Port int    `yaml:"port"`
	} `yaml:"server"`
	Spring struct {
		ElasticSearch struct {
			Host       string `yaml:"host"`
			Port       string `yaml:"port"`
			Scheme     string `yaml:"scheme"`
			Username   string `yaml:"username"`
			Password   string `yaml:"password"`
			CACertPath string `yaml:"caCertPath"`
		} `yaml:"elasticsearch"`
		Redis struct {
			Host string `yaml:"host"`
			Port string `yaml:"port"`
		} `yaml:"redis"`
	} `yaml:"spring"`
	OpenAI struct {
		APIKey string `yaml:"providerApiKey"`
	} `yaml:"openai"`
	Weaviate struct {
		Host         string `yaml:"host"`
		Scheme       string `yaml:"scheme"`
		OpenAIAPIKey string `yaml:"openAIAPIKey"`
	} `yaml:"weaviate"`
}

var AppConfig ApplicationConfig

func InitializeApplicationConfig(appConfigPath string) (defaultConf bool, err error) {
	conf, err := os.ReadFile(appConfigPath)
	if err == nil {
		if err = yaml.Unmarshal(conf, &AppConfig); err != nil {
			return
		}
	} else {

		err = nil
		defaultConf = true
		AppConfig.Environment = QA_ENV
		AppConfig.Spring.ElasticSearch.Host = "localhost"
		AppConfig.Spring.ElasticSearch.Port = "9995"
		AppConfig.Spring.ElasticSearch.Scheme = "http"
	}

	Environment = AppConfig.Environment
	return
}
