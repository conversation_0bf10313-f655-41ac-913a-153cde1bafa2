package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type TriggerPostScanRequest struct {
	TenantID    string         `json:"tenantId"`
	ServiceID   string         `json:"serviceId,omitempty"`
	Environment string         `json:"environment"`
	Params      PostScanParams `json:"params,omitempty"`
}

type PostScanParams struct {
	KeywordSearch             *bool `json:"keywordSearch,omitempty"`
	Campaigns                 *bool `json:"campaigns,omitempty"`
	MarkDeleted               *bool `json:"markDeleted,omitempty"`
	MarkAi                    *bool `json:"markAi,omitempty"`
	Enhancer                  *bool `json:"enhancer,omitempty"`
	IncidentWorkflow          *bool `json:"incidentWorkflow,omitempty"`
	SaveStatsStore            *bool `json:"saveStatsStore,omitempty"`
	EvaluatePostScanHeroStats *bool `json:"evaluatePostScanHeroStats,omitempty"`
	IdentityExtraction        *bool `json:"identityExtraction,omitempty"`
	ExtractAIResources        *bool `json:"extractAIResources,omitempty"`
	EvaluateVirtualResources  *bool `json:"evaluateVirtualResources,omitempty"`
	Prioritiser               *bool `json:"prioritiser,omitempty"`
	EnrichCloudResources      *bool `json:"enrichCloudResources,omitempty"`
	IdentityHeroStats         *bool `json:"identityHeroStats,omitempty"`
}

type PostScanResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

func main() {
	fmt.Println("=== Testing Post Scan API with Dynamic Authentication ===")
	
	// Test with a real tenant ID (replace with actual tenant ID)
	testTenantID := "F7rH044BdoQpTC85jdMT" // Example tenant ID from your description
	
	// Test 1: Basic request for all services
	fmt.Println("\n=== Test 1: Trigger for all services ===")
	testAllServices(testTenantID)

	// Test 2: Request for specific service
	fmt.Println("\n=== Test 2: Trigger for specific service (AWS) ===")
	testSpecificService(testTenantID)

	// Test 3: Request with custom parameters
	fmt.Println("\n=== Test 3: Trigger with custom parameters ===")
	testCustomParameters(testTenantID)

	// Test 4: Invalid request (missing tenantId)
	fmt.Println("\n=== Test 4: Invalid request (missing tenantId) ===")
	testInvalidRequest()
}

func testAllServices(tenantID string) {
	req := TriggerPostScanRequest{
		TenantID:    tenantID,
		Environment: "qa",
	}
	sendRequest(req)
}

func testSpecificService(tenantID string) {
	req := TriggerPostScanRequest{
		TenantID:    tenantID,
		ServiceID:   "aws",
		Environment: "qa",
	}
	sendRequest(req)
}

func testCustomParameters(tenantID string) {
	keywordSearch := true
	campaigns := false
	markDeleted := true

	req := TriggerPostScanRequest{
		TenantID:    tenantID,
		ServiceID:   "aws",
		Environment: "qa",
		Params: PostScanParams{
			KeywordSearch: &keywordSearch,
			Campaigns:     &campaigns,
			MarkDeleted:   &markDeleted,
		},
	}
	sendRequest(req)
}

func testInvalidRequest() {
	req := TriggerPostScanRequest{
		Environment: "qa",
		// Missing TenantID
	}
	sendRequest(req)
}

func sendRequest(req TriggerPostScanRequest) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		fmt.Printf("Error marshaling request: %v\n", err)
		return
	}

	fmt.Printf("Request: %s\n", string(jsonData))

	client := &http.Client{Timeout: 60 * time.Second} // Increased timeout for auth flow
	resp, err := client.Post(
		"http://localhost:19090/provider/post-scan/trigger",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		fmt.Printf("Error sending request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status: %s\n", resp.Status)
	fmt.Printf("Response: %s\n", string(body))

	// Pretty print the response
	var response PostScanResponse
	if err := json.Unmarshal(body, &response); err == nil {
		prettyJSON, _ := json.MarshalIndent(response, "", "  ")
		fmt.Printf("Pretty Response:\n%s\n", string(prettyJSON))
	}
}
