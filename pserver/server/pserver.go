package server

import (
	"net/http"

	"github.com/gorilla/mux"

	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/auth"
	custentity "github.com/precize/pserver/server/cust_entity"
	"github.com/precize/pserver/server/jira"
	textlookup "github.com/precize/pserver/server/text_lookup"
	"github.com/precize/pserver/server/weaviate"
	weaviateController "github.com/precize/pserver/server/weaviate/controller"
	weaviateService "github.com/precize/pserver/server/weaviate/service"
)

func StartProviderServer() {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("pserver")
		}
	}()

	// Initialize Weaviate service and controller
	weaviateService := weaviateService.NewWeaviateService(weaviate.GetClient())
	weaviateController := weaviateController.NewWeaviateController(weaviateService)

	r := mux.NewRouter()

	r.HandleFunc("/provider/auth/validate", auth.Validate).Methods("POST")

	r.HandleFunc("/provider/jira/issue/create", jira.CreateJiraIssue).Methods("POST")
	r.HandleFunc("/provider/jira/project/list", jira.ListJiraProjects).Methods("POST")
	r.HandleFunc("/provider/jira/issuetypefield/list", jira.ListJiraIssueTypeFields).Methods("POST")

	r.HandleFunc("/provider/custentity/update", custentity.UpdateCustomerEntityContext).Methods("POST")
	r.HandleFunc("/provider/text-lookup/ignore-apps", textlookup.AddIgnoreApps).Methods("POST")

	r.HandleFunc("/provider/post-scan/trigger", postscan.TriggerPostScan).Methods("POST")

	r.HandleFunc("/provider/weaviate/class/create", weaviateController.CreateClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/class/get", weaviateController.GetClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/class/delete", weaviateController.DeleteClass).Methods("POST")
	r.HandleFunc("/provider/weaviate/data/insert", weaviateController.InsertData).Methods("POST")
	r.HandleFunc("/provider/weaviate/data/insertById", weaviateController.InsertDataByID).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/all", weaviateController.SearchAll).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/id", weaviateController.SearchByID).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/query", weaviateController.SearchQuery).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/similar", weaviateController.SearchSimilar).Methods("POST")
	r.HandleFunc("/provider/weaviate/search/similar/bulk", weaviateController.SearchSimilarBulk).Methods("POST")
	r.HandleFunc("/provider/weaviate/delete/id", weaviateController.DeleteByID).Methods("POST")
	r.HandleFunc("/provider/weaviate/delete/query", weaviateController.DeleteByQuery).Methods("POST")

	r.Use(verifyMiddleware)

	// Start the server
	logger.Print(logger.INFO, "Starting server on port 19090")
	err := http.ListenAndServe(":19090", r)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to start server", err)
		return
	}
}

func verifyMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		// TODO: Any additional verifications
		next.ServeHTTP(w, r)
	})
}
