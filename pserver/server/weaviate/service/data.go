package service

import (
	"context"
	"fmt"

	"github.com/go-openapi/strfmt"
	"github.com/google/uuid"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/weaviate/model"
	"github.com/precize/pserver/server/weaviate/utils"
	weaviateInternalModel "github.com/weaviate/weaviate/entities/models"
)

func (s *WeaviateService) InsertData(ctx context.Context, req model.InsertDataRequest) (model.Response, error) {
	batcher := s.client.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		batcher = batcher.WithObjects(&weaviateInternalModel.Object{
			Class:      req.ClassName,
			Properties: obj,
		})
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		return model.Response{}, fmt.Errorf("failed to insert data: %w", err)
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		return model.Response{}, fmt.Errorf("some objects failed to insert: %v", errorMsgs)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	}, nil
}

func (s *WeaviateService) DeleteByID(ctx context.Context, req model.DeleteByIDRequest) (model.Response, error) {
	err := s.client.Data().Deleter().WithClassName(req.ClassName).WithID(req.ID).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to delete object: %v", err)
		return model.Response{}, fmt.Errorf("failed to delete object: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted object with ID '%s' from class '%s'", req.ID, req.ClassName),
	}, nil
}

func (s *WeaviateService) DeleteByQuery(ctx context.Context, req model.DeleteByQueryRequest) (model.Response, error) {
	if req.Output == "" {
		req.Output = "minimal"
	}

	batchDeleter := s.client.Batch().ObjectsBatchDeleter().WithClassName(req.ClassName).WithOutput(req.Output)
	for _, filter := range req.Filters {
		batchDeleter = batchDeleter.WithWhere(utils.BuildWhereFilter(filter))
	}

	response, err := batchDeleter.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch delete: %v", err)
		return model.Response{}, fmt.Errorf("failed to execute batch delete: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully executed batch delete on class '%s'", req.ClassName),
		Data:    response,
	}, nil
}

func (s *WeaviateService) InsertDataByID(ctx context.Context, req model.InsertDataRequest) (model.Response, error) {
	batcher := s.client.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		var id strfmt.UUID
		if objId, ok := obj["id"].(string); ok {
			parsedUUID := strfmt.UUID(objId)

			if _, err := uuid.Parse(string(parsedUUID)); err != nil {
				logger.Print(logger.ERROR, "Failed to parse UUID: %v", err)
				return model.Response{}, fmt.Errorf("failed to parse UUID: %w", err)
			}
			id = parsedUUID
			delete(obj, "id")
		}

		insertObj := &weaviateInternalModel.Object{
			Class:      req.ClassName,
			Properties: obj,
		}

		if len(id) > 0 {
			insertObj.ID = id
		}

		batcher = batcher.WithObjects(insertObj)
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		return model.Response{}, fmt.Errorf("failed to insert data: %w", err)
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		return model.Response{}, fmt.Errorf("some objects failed to insert: %v", errorMsgs)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	}, nil
}
