package service

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/pserver/server/weaviate/model"
	"github.com/precize/pserver/server/weaviate/utils"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
)

func (s *WeaviateService) SearchSimilarBulk(ctx context.Context, reqs []model.SearchSimilarRequest) (model.Response, error) {
	if len(reqs) == 0 {
		return model.Response{}, fmt.Errorf("no requests provided")
	}

	var queries []string
	for i, req := range reqs {

		if req.SearchType == "" {
			req.SearchType = "hybrid"
		}
		if req.Limit == 0 {
			req.Limit = 10
		}

		validSearchTypes := []string{"bm25", "vector", "hybrid"}
		if !slices.Contains(validSearchTypes, req.SearchType) {
			return model.Response{}, fmt.<PERSON><PERSON><PERSON>("searchType must be one of: bm25, vector, hybrid")
		}

		// Defaults for regulator
		if req.Regulator == nil {
			switch req.SearchType {
			case "hybrid":
				req.Regulator = utils.Float32Ptr(1.0)
			case "vector":
				// leave nil unless explicitly passed
			}
		}

		fields := []graphql.Field{}
		for _, fieldName := range req.Fields {
			fields = append(fields, graphql.Field{Name: fieldName})
		}

		switch req.SearchType {
		case "bm25":
			fields = append(fields, graphql.Field{
				Name: "_additional",
				Fields: []graphql.Field{
					{Name: "score"},
				},
			})
		case "vector":
			fields = append(fields, graphql.Field{
				Name: "_additional",
				Fields: []graphql.Field{
					{Name: "distance"},
				},
			})
		case "hybrid":
			fields = append(fields, graphql.Field{
				Name: "_additional",
				Fields: []graphql.Field{
					{Name: "score"},
					{Name: "explainScore"},
				},
			})
		}

		var arg string
		switch req.SearchType {
		case "bm25":
			arg = fmt.Sprintf(`(bm25: {query: "%s"`, req.Query)
			if len(req.Properties) > 0 {
				arg += fmt.Sprintf(` properties: [%s]`, `"`+strings.Join(req.Properties, `","`)+`"`)
			}
			arg += `})`
		case "vector":
			arg = fmt.Sprintf(`(nearText: {concepts: ["%s"]`, req.Query)
			if req.Regulator != nil {
				arg += fmt.Sprintf(` distance: %f`, *req.Regulator)
			}
			arg += `})`
		case "hybrid":
			alpha := "1.0"
			if req.Regulator != nil {
				alpha = fmt.Sprintf("%f", *req.Regulator)
			}
			arg = fmt.Sprintf(`(hybrid: {query: "%s" alpha: %s`, req.Query, alpha)
			if req.MaxVectorDistance != nil {
				arg += fmt.Sprintf(` maxVectorDistance: %f`, *req.MaxVectorDistance)
			}
			if len(req.Properties) > 0 {
				arg += fmt.Sprintf(` properties: [%s]`, `"`+strings.Join(req.Properties, `","`)+`"`)
			}
			if len(req.FusionType) > 0 {
				arg += fmt.Sprintf(` fusionType: %s`, req.FusionType)
			}
			arg += `})`
		}

		whereClause := ""
		if len(req.Filters) > 0 {
			where := utils.BuildWhereFilter(req.Filters[0])
			whereBytes, _ := json.Marshal(where)
			whereClause = fmt.Sprintf(` where: %s`, string(whereBytes))
		}

		autoCut := ""
		if req.AutoCut > 0 {
			autoCut = fmt.Sprintf(` autocut: %d`, req.AutoCut)
		}

		fieldStrs := []string{}
		for _, f := range fields {
			fieldStrs = append(fieldStrs, utils.BuildFieldString(f))
		}

		query := fmt.Sprintf(`
			q%d: Get {
				%s%s%s%s {
					%s
				}
			}`,
			i,
			req.ClassName,
			arg,
			whereClause,
			autoCut,
			strings.Join(fieldStrs, "\n"),
		)

		queries = append(queries, query)
	}

	rawQuery := fmt.Sprintf("query { %s }", strings.Join(queries, "\n"))

	result, err := s.client.GraphQL().Raw().WithQuery(rawQuery).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Bulk search failed: %v", err)
		return model.Response{}, fmt.Errorf("bulk search failed: %w", err)
	}

	return model.Response{
		Success: true,
		Message: "Successfully executed bulk search",
		Data:    result,
	}, nil
}

func (s *WeaviateService) SearchSimilar(ctx context.Context, req model.SearchSimilarRequest) (model.Response, error) {
	if req.SearchType == "" {
		req.SearchType = "hybrid"
	}

	validSearchTypes := []string{"bm25", "vector", "hybrid"}
	if !slices.Contains(validSearchTypes, req.SearchType) {
		return model.Response{}, fmt.Errorf("searchType must be one of: bm25, vector, hybrid")
	}

	if req.Regulator == nil {
		switch req.SearchType {
		case "hybrid":
			req.Regulator = utils.Float32Ptr(1.0)
		case "vector":
		}
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	gqlBuilder := s.client.GraphQL().Get().WithClassName(req.ClassName).WithLimit(req.Limit)
	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}

	switch req.SearchType {
	case "bm25":
		bm25Builder := (&graphql.BM25ArgumentBuilder{}).WithQuery(req.Query)
		if len(req.Properties) > 0 {
			bm25Builder = bm25Builder.WithProperties(req.Properties...)
		}
		gqlBuilder = gqlBuilder.WithBM25(bm25Builder)
		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}
		fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{{Name: "score"}}})

	case "vector":
		nearTextBuilder := s.client.GraphQL().NearTextArgBuilder().WithConcepts([]string{req.Query})
		if req.Regulator != nil {
			nearTextBuilder = nearTextBuilder.WithDistance(*req.Regulator)
		}
		gqlBuilder = gqlBuilder.WithNearText(nearTextBuilder)
		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}
		fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{{Name: "distance"}}})

	case "hybrid":
		hybridBuilder := s.client.GraphQL().HybridArgumentBuilder().WithQuery(req.Query)
		if req.Regulator != nil {
			hybridBuilder = hybridBuilder.WithAlpha(*req.Regulator)
		}
		if req.MaxVectorDistance != nil {
			hybridBuilder = hybridBuilder.WithMaxVectorDistance(*req.MaxVectorDistance)
		}
		if len(req.Properties) > 0 {
			hybridBuilder = hybridBuilder.WithProperties(req.Properties)
		}
		if len(req.FusionType) > 0 {
			hybridBuilder = hybridBuilder.WithFusionType(graphql.FusionType(req.FusionType))
		}
		gqlBuilder = gqlBuilder.WithHybrid(hybridBuilder)
		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}
		fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{{Name: "score"}, {Name: "explainScore"}}})
	}

	gqlBuilder = gqlBuilder.WithFields(fields...)
	for _, filter := range req.Filters {
		gqlBuilder = gqlBuilder.WithWhere(utils.BuildWhereFilter(filter))
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Search failed: %v", err)
		return model.Response{}, fmt.Errorf("search failed: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully searched class '%s'", req.ClassName),
		Data:    result,
	}, nil
}

func (s *WeaviateService) SearchAll(ctx context.Context, req model.SearchAllRequest) (model.Response, error) {
	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}

	additionalFields := "_additional { id"
	if req.IncludeVector {
		additionalFields += " vector"
	}
	additionalFields += " }"
	fields = append(fields, graphql.Field{Name: additionalFields})

	gqlBuilder := s.client.GraphQL().Get().WithClassName(req.ClassName).WithFields(fields...)
	if req.Limit > 0 {
		gqlBuilder = gqlBuilder.WithLimit(req.Limit)
	}
	if req.Cursor != "" {
		gqlBuilder = gqlBuilder.WithAfter(req.Cursor)
	}
	for _, filter := range req.Filters {
		gqlBuilder = gqlBuilder.WithWhere(utils.BuildWhereFilter(filter))
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "SearchAll failed: %v", err)
		return model.Response{}, fmt.Errorf("searchAll failed: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved data from class '%s'", req.ClassName),
		Data:    result,
	}, nil
}

func (s *WeaviateService) SearchByID(ctx context.Context, req model.SearchByIDRequest) (model.Response, error) {
	objects, err := s.client.Data().ObjectsGetter().WithClassName(req.ClassName).WithID(req.ID).WithVector().Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to get object by ID: %v", err)
		return model.Response{}, fmt.Errorf("failed to get object by ID: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved object with ID '%s' from class '%s'", req.ID, req.ClassName),
		Data:    objects,
	}, nil
}

func (s *WeaviateService) SearchQuery(ctx context.Context, req model.SearchQueryRequest) (model.Response, error) {
	if req.Limit == 0 {
		req.Limit = 10
	}

	gqlBuilder := s.client.GraphQL().Get().WithClassName(req.ClassName).WithLimit(req.Limit)
	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}
	gqlBuilder = gqlBuilder.WithFields(fields...)

	for _, filter := range req.Filters {
		gqlBuilder = gqlBuilder.WithWhere(utils.BuildWhereFilter(filter))
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Query search failed: %v", err)
		return model.Response{}, fmt.Errorf("query search failed: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully queried class '%s' with filters", req.ClassName),
		Data:    result,
	}, nil
}
