package postscan

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type PostScanService struct{}

func NewPostScanService() *PostScanService {
	return &PostScanService{}
}

func (s *PostScanService) ProcessPostScanTrigger(req TriggerPostScanRequest) (map[string]any, error) {
	result := make(map[string]any)
	var triggeredServices []string
	var errors []string

	if req.ServiceID != "" {
		var serviceID int
		for id, name := range serviceIDToName {
			if name == req.ServiceID {
				serviceID = id
				break
			}
		}

		collectedAt, err := s.getLatestCollectedAt(req.TenantID, serviceID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to get collectedAt for service %s: %v", req.ServiceID, err))
		} else if collectedAt == 0 {
			errors = append(errors, fmt.Sprintf("No scan data found for tenant %s and service %s", req.TenantID, req.ServiceID))
		} else {
			err := s.callPostScanAPI(req.ServiceID, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", req.ServiceID, err))
			} else {
				triggeredServices = append(triggeredServices, req.ServiceID)
			}
		}
	} else {
		serviceCollectedAtMap, err := s.getAllServicesCollectedAt(req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get services data: %v", err)
		}

		if len(serviceCollectedAtMap) == 0 {
			return nil, fmt.Errorf("no scan data found for tenant %s", req.TenantID)
		}

		for serviceID, collectedAt := range serviceCollectedAtMap {
			serviceName, ok := serviceIDToName[serviceID]
			if !ok {
				errors = append(errors, fmt.Sprintf("Unknown service ID: %d", serviceID))
				continue
			}

			err := s.callPostScanAPI(serviceName, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", serviceName, err))
			} else {
				triggeredServices = append(triggeredServices, serviceName)
			}
		}
	}

	result["triggeredServices"] = triggeredServices
	if len(errors) > 0 {
		result["errors"] = errors
	}

	return result, nil
}

func (s *PostScanService) getLatestCollectedAt(tenantID string, serviceID int) (int64, error) {
	query := fmt.Sprintf(`{
		"size": 1,
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"term": {
							"serviceId": %d
						}
					},
					{
						"match": {
							"status": "2"
						}
					},
					{
						"match": {
							"scanType": "0"
						}
					}
				]
			}
		},
		"sort": [
			{
				"collectedAt": {
					"order": "desc"
				}
			}
		],
		"_source": ["collectedAt", "id"]
	}`, tenantID, serviceID)

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{"scans"}, query)
	if err != nil {
		return 0, err
	}

	for _, doc := range docs {
		if collectedAtFloat, ok := doc["collectedAt"].(float64); ok {
			return int64(collectedAtFloat), nil
		}
	}

	return 0, nil
}

func (s *PostScanService) getAllServicesCollectedAt(tenantID string) (map[int]int64, error) {
	query := fmt.Sprintf(`{
		"size": 0,
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"status": "2"
						}
					},
					{
						"match": {
							"scanType": "0"
						}
					}
				]
			}
		},
		"aggs": {
			"by_service": {
				"terms": {
					"field": "serviceId",
					"size": 1000
				},
				"aggs": {
					"latest_collectedAt": {
						"top_hits": {
							"sort": [
								{
									"collectedAt": {
										"order": "desc"
									}
								}
							],
							"size": 1,
							"_source": {
								"includes": [
									"collectedAt"
								]
							}
						}
					}
				}
			}
		}
	}`, tenantID)

	eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{"scans"}, query)
	if err != nil {
		return nil, err
	}

	eventsAggBytes, err := json.Marshal(eventsAggregation)
	if err != nil {
		return nil, err
	}

	var result struct {
		ByService struct {
			Buckets []struct {
				Key               int `json:"key"` // serviceId
				LatestCollectedAt struct {
					Hits struct {
						Hits []struct {
							Source struct {
								CollectedAt int64 `json:"collectedAt"`
							} `json:"_source"`
						} `json:"hits"`
					} `json:"hits"`
				} `json:"latest_collectedAt"`
			} `json:"buckets"`
		} `json:"by_service"`
	}

	if err := json.Unmarshal(eventsAggBytes, &result); err != nil {
		return nil, err
	}

	serviceCollectedAtMap := make(map[int]int64)
	for _, serviceBucket := range result.ByService.Buckets {
		if len(serviceBucket.LatestCollectedAt.Hits.Hits) > 0 {
			serviceCollectedAtMap[serviceBucket.Key] = serviceBucket.LatestCollectedAt.Hits.Hits[0].Source.CollectedAt
		}
	}

	return serviceCollectedAtMap, nil
}

func (s *PostScanService) getTenantSupportUser(tenantID string) (*TenantUser, error) {
	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"wildcard": {
							"tenantId.keyword": "%s"
						}
					}
				],
				"should": [
					{
						"wildcard": {
							"username.keyword": "support_*"
						}
					},
					{
						"term": {
							"username.keyword": "<EMAIL>"
						}
					}
				],
				"minimum_should_match": 1
			}
		},
		"size": 1,
		"from": 0
	}`, tenantID)

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{"tenant_user"}, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query tenant_user index: %v", err)
	}

	for _, doc := range docs {
		var user TenantUser
		docBytes, err := json.Marshal(doc)
		if err != nil {
			continue
		}

		if err := json.Unmarshal(docBytes, &user); err != nil {
			continue
		}

		return &user, nil
	}

	return nil, fmt.Errorf("no support user found for tenant %s", tenantID)
}

func (s *PostScanService) getAuthToken(username, baseURL string) (string, error) {
	loginReq := LoginRequest{
		Username:   username,
		Password:   "Gov3rnanc3@Pr3c!z3",
		RememberMe: false,
	}

	jsonData, err := json.Marshal(loginReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal login request: %v", err)
	}

	loginURL := baseURL + "/precize/login"

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Post(loginURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to call login API: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read login response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login API failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", fmt.Errorf("failed to parse login response: %v", err)
	}

	if loginResp.Status != 200 || loginResp.Data.AccessToken == "" {
		return "", fmt.Errorf("login failed: %s", loginResp.Message)
	}

	return loginResp.Data.AccessToken, nil
}

func (s *PostScanService) callPostScanAPI(serviceID string, tenantID string, collectedAt int64, env string, params PostScanParams) error {
	keywordSearch := getBoolValue(params.KeywordSearch, true)
	campaigns := getBoolValue(params.Campaigns, true)
	markDeleted := getBoolValue(params.MarkDeleted, true)
	markAi := getBoolValue(params.MarkAi, true)
	enhancer := getBoolValue(params.Enhancer, true)
	incidentWorkflow := getBoolValue(params.IncidentWorkflow, true)
	saveStatsStore := getBoolValue(params.SaveStatsStore, true)
	evaluatePostScanHeroStats := getBoolValue(params.EvaluatePostScanHeroStats, true)
	identityExtraction := getBoolValue(params.IdentityExtraction, true)
	extractAIResources := getBoolValue(params.ExtractAIResources, true)
	evaluateVirtualResources := getBoolValue(params.EvaluateVirtualResources, true)
	prioritiser := getBoolValue(params.Prioritiser, true)
	enrichCloudResources := getBoolValue(params.EnrichCloudResources, true)
	identityHeroStats := getBoolValue(params.IdentityHeroStats, true)

	var baseURL string
	switch env {
	case "qa":
		baseURL = "https://qa.precize.ai"
	case "preprod":
		baseURL = "https://preprod.precize.ai"
	case "prod":
		baseURL = "https://web.precize.ai"
	default:
		return fmt.Errorf("invalid environment: %s", env)
	}

	user, err := s.getTenantSupportUser(tenantID)
	if err != nil {
		return fmt.Errorf("failed to get tenant support user: %v", err)
	}

	authToken, err := s.getAuthToken(user.Username, baseURL)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %v", err)
	}

	apiURL := baseURL + "/precize/resources/run/evaluatePostScanHeroStats"
	url := fmt.Sprintf(
		"%s/%s/%s/%d?keywordSearch=%t&campaigns=%t&markDeleted=%t&markAi=%t&enhancer=%t&incidentWorkflow=%t&saveStatsStore=%t&evaluatePostScanHeroStats=%t&identityExtraction=%t&extractAIResources=%t&evaluateVirtualResources=%t&prioritiser=%t&enrichCloudResources=%t&identityHeroStats=%t",
		apiURL, serviceID, tenantID, collectedAt,
		keywordSearch, campaigns, markDeleted, markAi, enhancer, incidentWorkflow, saveStatsStore,
		evaluatePostScanHeroStats, identityExtraction, extractAIResources, evaluateVirtualResources,
		prioritiser, enrichCloudResources, identityHeroStats,
	)

	headers := map[string]string{
		"Authorization": "Bearer " + authToken,
		"Content-Type":  "application/json",
	}

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	io.Copy(io.Discard, resp.Body)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		return fmt.Errorf("API call failed with status: %s", resp.Status)
	}

	logger.Print(logger.INFO, "API call succeeded", []string{tenantID}, serviceID, collectedAt)
	return nil
}

func getBoolValue(ptr *bool, defaultValue bool) bool {
	if ptr == nil {
		return defaultValue
	}
	return *ptr
}
