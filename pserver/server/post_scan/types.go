package postscan

var serviceIDToName = map[int]string{
	1000: "aws",
	2000: "azure",
	3000: "gcp",
}

type TriggerPostScanRequest struct {
	TenantID    string         `json:"tenantId"`
	ServiceID   string         `json:"serviceId,omitempty"` // Optional: if empty, trigger for all services
	Environment string         `json:"environment"`         // qa, preprod, prod
	Params      PostScanParams `json:"params,omitempty"`    // Optional parameters
}

type PostScanParams struct {
	KeywordSearch             *bool `json:"keywordSearch,omitempty"`
	Campaigns                 *bool `json:"campaigns,omitempty"`
	MarkDeleted               *bool `json:"markDeleted,omitempty"`
	MarkAi                    *bool `json:"markAi,omitempty"`
	Enhancer                  *bool `json:"enhancer,omitempty"`
	IncidentWorkflow          *bool `json:"incidentWorkflow,omitempty"`
	SaveStatsStore            *bool `json:"saveStatsStore,omitempty"`
	EvaluatePostScanHeroStats *bool `json:"evaluatePostScanHeroStats,omitempty"`
	IdentityExtraction        *bool `json:"identityExtraction,omitempty"`
	ExtractAIResources        *bool `json:"extractAIResources,omitempty"`
	EvaluateVirtualResources  *bool `json:"evaluateVirtualResources,omitempty"`
	Prioritiser               *bool `json:"prioritiser,omitempty"`
	EnrichCloudResources      *bool `json:"enrichCloudResources,omitempty"`
	IdentityHeroStats         *bool `json:"identityHeroStats,omitempty"`
}

type PostScanResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

type AggregationResult struct {
	ByTenant struct {
		Buckets []struct {
			Key       string `json:"key"` // tenantId
			ByService struct {
				Buckets []struct {
					Key               int `json:"key"` // serviceId
					LatestCollectedAt struct {
						Hits struct {
							Hits []struct {
								Source struct {
									CollectedAt int64 `json:"collectedAt"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"latest_collectedAt"`
				} `json:"buckets"`
			} `json:"by_service"`
		} `json:"buckets"`
	} `json:"by_tenant"`
}

type TenantUser struct {
	ID       string `json:"id"`
	TenantID string `json:"tenantId"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Username string `json:"username"`
	Password string `json:"password"`
	Type     int    `json:"type"`
}

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	RememberMe bool   `json:"rememberMe"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Status  int    `json:"status"`
	Error   string `json:"error"`
	Data    struct {
		TenantID          string   `json:"tenantId"`
		CompanyName       string   `json:"companyName"`
		Role              string   `json:"role"`
		UserID            string   `json:"userId"`
		Username          string   `json:"username"`
		Email             string   `json:"email"`
		Type              int      `json:"type"`
		Name              string   `json:"name"`
		AccessToken       string   `json:"access_token"`
		OnboardedServices []string `json:"onboardedServices"`
	} `json:"data"`
}
