package postscan

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

var serviceIDToName = map[int]string{
	1000: "aws",
	2000: "azure",
	3000: "gcp",
}

type TriggerPostScanRequest struct {
	TenantID    string         `json:"tenantId"`
	ServiceID   string         `json:"serviceId,omitempty"`
	Environment string         `json:"environment"`
	Params      PostScanParams `json:"params,omitempty"`
}

type PostScanParams struct {
	KeywordSearch             *bool `json:"keywordSearch,omitempty"`
	Campaigns                 *bool `json:"campaigns,omitempty"`
	MarkDeleted               *bool `json:"markDeleted,omitempty"`
	MarkAi                    *bool `json:"markAi,omitempty"`
	Enhancer                  *bool `json:"enhancer,omitempty"`
	IncidentWorkflow          *bool `json:"incidentWorkflow,omitempty"`
	SaveStatsStore            *bool `json:"saveStatsStore,omitempty"`
	EvaluatePostScanHeroStats *bool `json:"evaluatePostScanHeroStats,omitempty"`
	IdentityExtraction        *bool `json:"identityExtraction,omitempty"`
	ExtractAIResources        *bool `json:"extractAIResources,omitempty"`
	EvaluateVirtualResources  *bool `json:"evaluateVirtualResources,omitempty"`
	Prioritiser               *bool `json:"prioritiser,omitempty"`
	EnrichCloudResources      *bool `json:"enrichCloudResources,omitempty"`
	IdentityHeroStats         *bool `json:"identityHeroStats,omitempty"`
}

type PostScanResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

type AggregationResult struct {
	ByTenant struct {
		Buckets []struct {
			Key       string `json:"key"` // tenantId
			ByService struct {
				Buckets []struct {
					Key               int `json:"key"` // serviceId
					LatestCollectedAt struct {
						Hits struct {
							Hits []struct {
								Source struct {
									CollectedAt int64 `json:"collectedAt"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"latest_collectedAt"`
				} `json:"buckets"`
			} `json:"by_service"`
		} `json:"buckets"`
	} `json:"by_tenant"`
}

type TenantUser struct {
	ID       string `json:"id"`
	TenantID string `json:"tenantId"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Username string `json:"username"`
	Password string `json:"password"`
	Type     int    `json:"type"`
}

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	RememberMe bool   `json:"rememberMe"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Status  int    `json:"status"`
	Error   string `json:"error"`
	Data    struct {
		TenantID          string   `json:"tenantId"`
		CompanyName       string   `json:"companyName"`
		Role              string   `json:"role"`
		UserID            string   `json:"userId"`
		Username          string   `json:"username"`
		Email             string   `json:"email"`
		Type              int      `json:"type"`
		Name              string   `json:"name"`
		AccessToken       string   `json:"access_token"`
		OnboardedServices []string `json:"onboardedServices"`
	} `json:"data"`
}

func TriggerPostScan(w http.ResponseWriter, r *http.Request) {
	logger.Print(logger.INFO, "Request received to trigger post scan")

	if r.Method != http.MethodPost {
		writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		writeError(w, http.StatusInternalServerError, "Failed to read request body")
		return
	}

	var triggerReq TriggerPostScanRequest
	if err := json.Unmarshal(req, &triggerReq); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON", err)
		writeError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if triggerReq.TenantID == "" {
		writeError(w, http.StatusBadRequest, "tenantId is required")
		return
	}

	if triggerReq.Environment == "" {
		writeError(w, http.StatusBadRequest, "environment is required")
		return
	}

	if triggerReq.Environment != "qa" && triggerReq.Environment != "preprod" && triggerReq.Environment != "prod" {
		writeError(w, http.StatusBadRequest, "environment must be one of: qa, preprod, prod")
		return
	}

	if triggerReq.ServiceID != "" {
		validService := false
		for _, serviceName := range serviceIDToName {
			if serviceName == triggerReq.ServiceID {
				validService = true
				break
			}
		}
		if !validService {
			writeError(w, http.StatusBadRequest, "serviceId must be one of: aws, azure, gcp")
			return
		}
	}

	result, err := processPostScanTrigger(triggerReq)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to process post scan trigger", err)
		writeError(w, http.StatusInternalServerError, err.Error())
		return
	}

	writeSuccess(w, "Post scan triggered successfully", result)
}

func writeResponse(w http.ResponseWriter, status int, response PostScanResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Print(logger.ERROR, "Failed to write response", err)
	}
}

func writeError(w http.ResponseWriter, status int, message string) {
	writeResponse(w, status, PostScanResponse{
		Success: false,
		Message: message,
	})
}

func writeSuccess(w http.ResponseWriter, message string, data any) {
	writeResponse(w, http.StatusOK, PostScanResponse{
		Success: true,
		Message: message,
		Data:    data,
	})
}

func processPostScanTrigger(req TriggerPostScanRequest) (map[string]any, error) {
	result := make(map[string]any)
	var triggeredServices []string
	var errors []string

	if req.ServiceID != "" {

		var serviceID int
		for id, name := range serviceIDToName {
			if name == req.ServiceID {
				serviceID = id
				break
			}
		}

		collectedAt, err := getLatestCollectedAt(req.TenantID, serviceID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to get collectedAt for service %s: %v", req.ServiceID, err))
		} else if collectedAt == 0 {
			errors = append(errors, fmt.Sprintf("No scan data found for tenant %s and service %s", req.TenantID, req.ServiceID))
		} else {
			err := callPostScanAPI(req.ServiceID, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", req.ServiceID, err))
			} else {
				triggeredServices = append(triggeredServices, req.ServiceID)
			}
		}
	} else {

		serviceCollectedAtMap, err := getAllServicesCollectedAt(req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get services data: %v", err)
		}

		if len(serviceCollectedAtMap) == 0 {
			return nil, fmt.Errorf("no scan data found for tenant %s", req.TenantID)
		}

		for serviceID, collectedAt := range serviceCollectedAtMap {
			serviceName, ok := serviceIDToName[serviceID]
			if !ok {
				errors = append(errors, fmt.Sprintf("Unknown service ID: %d", serviceID))
				continue
			}

			err := callPostScanAPI(serviceName, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", serviceName, err))
			} else {
				triggeredServices = append(triggeredServices, serviceName)
			}
		}
	}

	result["triggeredServices"] = triggeredServices
	if len(errors) > 0 {
		result["errors"] = errors
	}

	return result, nil
}

func getLatestCollectedAt(tenantID string, serviceID int) (int64, error) {
	query := fmt.Sprintf(`{
		"size": 1,
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"term": {
							"serviceId": %d
						}
					},
					{
						"match": {
							"status": "2"
						}
					},
					{
						"match": {
							"scanType": "0"
						}
					}
				]
			}
		},
		"sort": [
			{
				"collectedAt": {
					"order": "desc"
				}
			}
		],
		"_source": ["collectedAt", "id"]
	}`, tenantID, serviceID)

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{"scans"}, query)
	if err != nil {
		return 0, err
	}

	for _, doc := range docs {
		if collectedAtFloat, ok := doc["collectedAt"].(float64); ok {
			return int64(collectedAtFloat), nil
		}
	}

	return 0, nil
}

func getAllServicesCollectedAt(tenantID string) (map[int]int64, error) {
	query := fmt.Sprintf(`{
		"size": 0,
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"status": "2"
						}
					},
					{
						"match": {
							"scanType": "0"
						}
					}
				]
			}
		},
		"aggs": {
			"by_service": {
				"terms": {
					"field": "serviceId",
					"size": 1000
				},
				"aggs": {
					"latest_collectedAt": {
						"top_hits": {
							"sort": [
								{
									"collectedAt": {
										"order": "desc"
									}
								}
							],
							"size": 1,
							"_source": {
								"includes": [
									"collectedAt"
								]
							}
						}
					}
				}
			}
		}
	}`, tenantID)

	eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{"scans"}, query)
	if err != nil {
		return nil, err
	}

	eventsAggBytes, err := json.Marshal(eventsAggregation)
	if err != nil {
		return nil, err
	}

	var result struct {
		ByService struct {
			Buckets []struct {
				Key               int `json:"key"` // serviceId
				LatestCollectedAt struct {
					Hits struct {
						Hits []struct {
							Source struct {
								CollectedAt int64 `json:"collectedAt"`
							} `json:"_source"`
						} `json:"hits"`
					} `json:"hits"`
				} `json:"latest_collectedAt"`
			} `json:"buckets"`
		} `json:"by_service"`
	}

	if err := json.Unmarshal(eventsAggBytes, &result); err != nil {
		return nil, err
	}

	serviceCollectedAtMap := make(map[int]int64)
	for _, serviceBucket := range result.ByService.Buckets {
		if len(serviceBucket.LatestCollectedAt.Hits.Hits) > 0 {
			serviceCollectedAtMap[serviceBucket.Key] = serviceBucket.LatestCollectedAt.Hits.Hits[0].Source.CollectedAt
		}
	}

	return serviceCollectedAtMap, nil
}

func getAuthToken(env, baseURL string) (string, error) {
	loginReq := LoginRequest{
		Username:   "<EMAIL>",
		Password:   "Gov3rnanc3@Pr3c!z3",
		RememberMe: false,
	}

	// if env == "preprod" {
	// 	loginReq.Username = "<EMAIL>"
	// }

	jsonData, err := json.Marshal(loginReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal login request: %v", err)
	}

	loginURL := baseURL + "/precize/login"

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Post(loginURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to call login API: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read login response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login API failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", fmt.Errorf("failed to parse login response: %v", err)
	}

	if loginResp.Status != 200 || loginResp.Data.AccessToken == "" {
		return "", fmt.Errorf("login failed: %s", loginResp.Message)
	}

	return loginResp.Data.AccessToken, nil
}

func callPostScanAPI(serviceID string, tenantID string, collectedAt int64, env string, params PostScanParams) error {

	keywordSearch := getBoolValue(params.KeywordSearch, true)
	campaigns := getBoolValue(params.Campaigns, true)
	markDeleted := getBoolValue(params.MarkDeleted, true)
	markAi := getBoolValue(params.MarkAi, true)
	enhancer := getBoolValue(params.Enhancer, true)
	incidentWorkflow := getBoolValue(params.IncidentWorkflow, true)
	saveStatsStore := getBoolValue(params.SaveStatsStore, true)
	evaluatePostScanHeroStats := getBoolValue(params.EvaluatePostScanHeroStats, true)
	identityExtraction := getBoolValue(params.IdentityExtraction, true)
	extractAIResources := getBoolValue(params.ExtractAIResources, true)
	evaluateVirtualResources := getBoolValue(params.EvaluateVirtualResources, true)
	prioritiser := getBoolValue(params.Prioritiser, true)
	enrichCloudResources := getBoolValue(params.EnrichCloudResources, true)
	identityHeroStats := getBoolValue(params.IdentityHeroStats, true)

	// Determine base URL based on environment
	var baseURL string
	switch env {
	case "qa":
		baseURL = "https://qa.precize.ai"
	case "preprod":
		baseURL = "https://preprod.precize.ai"
	case "prod":
		baseURL = "https://web.precize.ai"
	default:
		return fmt.Errorf("invalid environment: %s", env)
	}

	authToken, err := getAuthToken(env, baseURL)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %v", err)
	}

	apiURL := baseURL + "/precize/resources/run/evaluatePostScanHeroStats"
	url := fmt.Sprintf(
		"%s/%s/%s/%d?keywordSearch=%t&campaigns=%t&markDeleted=%t&markAi=%t&enhancer=%t&incidentWorkflow=%t&saveStatsStore=%t&evaluatePostScanHeroStats=%t&identityExtraction=%t&extractAIResources=%t&evaluateVirtualResources=%t&prioritiser=%t&enrichCloudResources=%t&identityHeroStats=%t",
		apiURL, serviceID, tenantID, collectedAt,
		keywordSearch, campaigns, markDeleted, markAi, enhancer, incidentWorkflow, saveStatsStore,
		evaluatePostScanHeroStats, identityExtraction, extractAIResources, evaluateVirtualResources,
		prioritiser, enrichCloudResources, identityHeroStats,
	)

	headers := map[string]string{
		"Authorization": "Bearer " + authToken,
		"Content-Type":  "application/json",
	}

	logger.Print(logger.INFO, "Calling post scan API", []string{tenantID}, serviceID, collectedAt, url)

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	io.Copy(io.Discard, resp.Body)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		return fmt.Errorf("API call failed with status: %s", resp.Status)
	}

	logger.Print(logger.INFO, "API call succeeded", []string{tenantID}, serviceID, collectedAt)
	return nil
}

func getBoolValue(ptr *bool, defaultValue bool) bool {
	if ptr == nil {
		return defaultValue
	}
	return *ptr
}
