package postscan

import (
	"encoding/json"
	"io"
	"net/http"

	"github.com/precize/logger"
)

func TriggerPostScan(w http.ResponseWriter, r *http.Request) {
	logger.Print(logger.INFO, "Request received to trigger post scan")

	if r.Method != http.MethodPost {
		writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		writeError(w, http.StatusInternalServerError, "Failed to read request body")
		return
	}

	var triggerReq PostScanRequest
	if err := json.Unmarshal(req, &triggerReq); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON", err)
		writeError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if triggerReq.TenantID == "" {
		writeError(w, http.StatusBadRequest, "tenantId is required")
		return
	}

	if triggerReq.Environment == "" {
		writeError(w, http.StatusBadRequest, "environment is required")
		return
	}

	if triggerReq.Environment != "qa" && triggerReq.Environment != "preprod" && triggerReq.Environment != "prod" {
		writeError(w, http.StatusBadRequest, "environment must be one of: qa, preprod, prod")
		return
	}

	if triggerReq.ServiceID != "" {
		validService := false
		for _, serviceName := range serviceIDToName {
			if serviceName == triggerReq.ServiceID {
				validService = true
				break
			}
		}
		if !validService {
			writeError(w, http.StatusBadRequest, "serviceId must be one of: aws, azure, gcp")
			return
		}
	}

	result, err := ProcessPostScanTrigger(triggerReq)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to process post scan trigger", err)
		writeError(w, http.StatusInternalServerError, err.Error())
		return
	}

	writeSuccess(w, "Post scan triggered successfully", result)
}

func writeResponse(w http.ResponseWriter, status int, response PostScanResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Print(logger.ERROR, "Failed to write response", err)
	}
}

func writeError(w http.ResponseWriter, status int, message string) {
	writeResponse(w, status, PostScanResponse{
		Success: false,
		Message: message,
	})
}

func writeSuccess(w http.ResponseWriter, message string, data any) {
	writeResponse(w, http.StatusOK, PostScanResponse{
		Success: true,
		Message: message,
		Data:    data,
	})
}
