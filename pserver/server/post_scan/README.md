# Post Scan API with Dynamic Authentication

## Overview

This API dynamically triggers post scan operations by:
1. Querying the `tenant_user` index to find the support user for the tenant
2. Calling the login API to get a valid authentication token
3. Using that token to trigger the post scan API

## API Endpoint
```
POST /provider/post-scan/trigger
```

## Authentication Flow

The API automatically handles authentication by:
1. **Finding Support User**: Queries `tenant_user` index with pattern `support_*` for the given tenant
2. **Getting Token**: Calls `/precize/login` with the support user credentials  
3. **Making API Call**: Uses the obtained token to call the post scan API

## Request Examples

### Basic Request (All Services)
```json
{
  "tenantId": "F7rH044BdoQpTC85jdMT",
  "environment": "qa"
}
```

### Specific Service
```json
{
  "tenantId": "F7rH044BdoQpTC85jdMT",
  "serviceId": "aws",
  "environment": "qa"
}
```

### With Custom Parameters
```json
{
  "tenantId": "F7rH044BdoQpTC85jdMT",
  "serviceId": "aws",
  "environment": "qa",
  "params": {
    "keywordSearch": true,
    "campaigns": false,
    "markDeleted": true,
    "markAi": false,
    "enhancer": true,
    "incidentWorkflow": true,
    "saveStatsStore": true,
    "evaluatePostScanHeroStats": true,
    "identityExtraction": true,
    "extractAIResources": false,
    "evaluateVirtualResources": true,
    "prioritiser": true,
    "enrichCloudResources": true,
    "identityHeroStats": true
  }
}
```

## Response Format

### Success
```json
{
  "success": true,
  "message": "Post scan triggered successfully",
  "data": {
    "triggeredServices": ["aws", "azure", "gcp"]
  }
}
```

### Partial Success
```json
{
  "success": true,
  "message": "Post scan triggered successfully",
  "data": {
    "triggeredServices": ["aws"],
    "errors": ["Failed to trigger post scan for service azure: login failed"]
  }
}
```

## cURL Example
```bash
curl -X POST http://localhost:19090/provider/post-scan/trigger \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "F7rH044BdoQpTC85jdMT",
    "environment": "qa"
  }'
```

## Prerequisites

1. **Tenant User**: Support user with pattern `support_*` must exist in `tenant_user` index
2. **Scan Data**: Completed scans must exist in `scans` index with `status: "2"` and `scanType: "0"`
3. **Environment Access**: Target environment must be accessible

## Parameters

All parameters default to `true` if not specified:
- `keywordSearch`, `campaigns`, `markDeleted`, `markAi`, `enhancer`
- `incidentWorkflow`, `saveStatsStore`, `evaluatePostScanHeroStats`
- `identityExtraction`, `extractAIResources`, `evaluateVirtualResources`
- `prioritiser`, `enrichCloudResources`, `identityHeroStats`

## Environments

- `qa`: https://qa.precize.ai
- `preprod`: https://preprod.precize.ai
- `prod`: https://web.precize.ai
