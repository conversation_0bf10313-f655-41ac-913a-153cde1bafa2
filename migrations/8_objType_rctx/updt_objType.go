package main

import (
	"encoding/json"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
	"github.com/precize/provider/wiz/types"
)

type PrimaryContext struct {
	Owners      []string
	Env         []string
	App         []string
	Team        []string
	Software    []string
	Deployment  []string
	Sensitivity []string
	Compliance  []string
	CostCenter  []string
	TTL         []string
}

const (
	MAX_THREADS = 10
)

func UpdateObjTypeForRctx() {
	rctxAggsQuery := `{"query":{"match_all":{}},"from":0,"size":0,"sort":[],"aggs":{"tenant":{"terms":{"field":"tenantId.keyword","size":10000},"aggs":{"lastCollected":{"terms":{"field":"lastCollectedAt.keyword","size":10000}}}}}}`
	rctxAggsDocs, err := elastic.ExecuteSearchForAggregation([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxAggsQuery)
	if err != nil {
		return
	}

	jobs := make(chan [2]string, 100)
	var wg sync.WaitGroup

	for i := 0; i < MAX_THREADS; i++ {
		go func() {
			for job := range jobs {
				UpdateObjTypeForRctxForTenantAndCollectedAt(job[0], job[1])
				wg.Done()
			}
		}()
	}

	for _, tenant := range rctxAggsDocs["tenant"].(map[string]any)["buckets"].([]any) {
		tenantID := tenant.(map[string]any)["key"].(string)
		for _, lastCollected := range tenant.(map[string]any)["lastCollected"].(map[string]any)["buckets"].([]any) {
			lastCollectedAt := lastCollected.(map[string]any)["key"].(string)
			wg.Add(1)
			jobs <- [2]string{tenantID, lastCollectedAt}
		}
	}

	close(jobs)
	wg.Wait()
}

func UpdateObjTypeForRctxForTenantAndCollectedAt(tenantID, lastCollectedAt string) {

	if len(tenantID) <= 0 || len(lastCollectedAt) <= 0 {
		return
	}

	logger.Print(logger.INFO, "Processing started for tenant and collectedAt", []string{tenantID, lastCollectedAt})

	resourcesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"collectedAt":"` + lastCollectedAt + `"}}]}}}`
	var (
		searchAfter                any
		bulkResourceContextRequest strings.Builder
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		var (
			collectedRctxDocIDs []string
			primaryContext      = make(map[string]PrimaryContext)
			rctxToCR            = make(map[string]string)
		)

		for resourcesDocID, crDoc := range resourcesDocs {
			crDocBytes, err := json.Marshal(crDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var crDoc types.CloudResource
			if err = json.Unmarshal(crDocBytes, &crDoc); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			if slices.Contains([]string{common.AZURE_LOCATION_RESOURCE_TYPE, common.AZURE_ASSIGNEDROLE_RESOURCE_TYPE, common.AZURE_GROUPS_RESOURCE_TYPE, common.AZURE_POLICYSTATE_RESOURCE_TYPE, common.AZURE_POLICYDEFINITION_RESOURCE_TYPE, common.AWS_REGION_RESOURCE_TYPE, common.GCP_REGION_RESOURCE_TYPE, common.GCP_CONSTRAINT_RESOURCE_TYPE, common.APP_RESOURCE_TYPE}, crDoc.EntityType) {
				continue
			}

			primaryContext[resourcesDocID] = PrimaryContext{
				Owners:      crDoc.Owner,
				Env:         crDoc.Environment,
				App:         crDoc.App,
				Team:        crDoc.Team,
				Software:    crDoc.Software,
				Deployment:  crDoc.Deployment,
				Sensitivity: crDoc.Sensitivity,
				Compliance:  crDoc.Compliance,
				CostCenter:  crDoc.CostCenter,
				TTL:         crDoc.TTL,
			}

			contextDocID := ""
			switch crDoc.EntityType {
			case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
				common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
				common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:
				contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.EntityID, lastCollectedAt, tenantID)
			default:
				contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, lastCollectedAt, tenantID)
			}

			collectedRctxDocIDs = append(collectedRctxDocIDs, contextDocID)
			rctxToCR[contextDocID] = resourcesDocID
		}

		var (
			searchAfter any
		)

		for {
			rctxQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"lastCollectedAt":"` + lastCollectedAt + `"}},{"terms":{"id.keyword":["` + strings.Join(collectedRctxDocIDs, `","`) + `"]}}]}}}`
			rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
			if err != nil {
				return
			}

			if len(rctxDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			logger.Print(logger.INFO, "Resource context fetched", len(rctxDocs))

			for docID, rctxDoc := range rctxDocs {

				if i := slices.Index(collectedRctxDocIDs, docID); i != -1 {
					collectedRctxDocIDs = slices.Delete(collectedRctxDocIDs, i, i+1)
				}

				var rctx common.ResourceContextInsertDoc
				jsonBytes, er := json.Marshal(rctxDoc)
				if er != nil {
					continue
				}
				err = json.Unmarshal(jsonBytes, &rctx)
				if err != nil {
					continue
				}

				crDocID := common.GenerateCombinedHashIDCaseSensitive(rctx.TenantID, rctx.LastCollectedAt, strings.ToLower(rctx.Account), strings.ToLower(rctx.ResourceID), rctx.ResourceType)
				if primaryContext, ok := primaryContext[crDocID]; ok {
					UpdateObjTypeForRctxForDoc(&rctx, primaryContext)
				} else {
					logger.Print(logger.INFO, "Primary context not found", crDocID)
					continue
				}

				resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				resourceContextInsertDoc, err := json.Marshal(rctx)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					return
				}

				bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
				bulkResourceContextRequest.WriteString("\n")
				bulkResourceContextRequest.Write(resourceContextInsertDoc)
				bulkResourceContextRequest.WriteString("\n")
			}
		}

		if len(collectedRctxDocIDs) > 0 {
			logger.Print(logger.INFO, "Resource context not found", len(collectedRctxDocIDs))

			crIDs := make([]string, 0)
			for _, rctx := range collectedRctxDocIDs {
				crIDs = append(crIDs, rctxToCR[rctx])
			}

			var (
				collectedRctxDocIDs []string
				primaryContext      = make(map[string]PrimaryContext)
			)

			crQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(crIDs, `","`) + `"]}}]}}}`

			resourcesDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
			if err != nil {
				return
			}

			for _, crDoc := range resourcesDocs {

				if resourcesDocID, ok := crDoc["id"].(string); ok {

					crDocBytes, err := json.Marshal(crDoc)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
						continue
					}

					var crDoc types.CloudResource
					if err = json.Unmarshal(crDocBytes, &crDoc); err != nil {
						logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
						continue
					}

					primaryContext[resourcesDocID] = PrimaryContext{
						Owners:      crDoc.Owner,
						Env:         crDoc.Environment,
						App:         crDoc.App,
						Team:        crDoc.Team,
						Software:    crDoc.Software,
						Deployment:  crDoc.Deployment,
						Sensitivity: crDoc.Sensitivity,
						Compliance:  crDoc.Compliance,
						CostCenter:  crDoc.CostCenter,
						TTL:         crDoc.TTL,
					}

					// without account ID

					contextDocID := ""
					switch crDoc.EntityType {
					case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
						common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
						common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:
						contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.EntityID, tenantID)
					default:
						contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, tenantID)
					}

					collectedRctxDocIDs = append(collectedRctxDocIDs, contextDocID)
				}
			}

			var (
				searchAfter any
			)

			for {

				rctxQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"lastCollectedAt":"` + lastCollectedAt + `"}},{"terms":{"id.keyword":["` + strings.Join(collectedRctxDocIDs, `","`) + `"]}}]}}}`
				rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
				if err != nil {
					return
				}

				if len(rctxDocs) > 0 {
					searchAfter = sortResponse
				} else {
					break
				}

				logger.Print(logger.INFO, "Resource context fetched", len(rctxDocs))

				for docID, rctxDoc := range rctxDocs {

					collectedRctxDocIDs = slices.DeleteFunc(collectedRctxDocIDs, func(id string) bool {
						return id == docID
					})

					var rctx common.ResourceContextInsertDoc
					jsonBytes, er := json.Marshal(rctxDoc)
					if er != nil {
						continue
					}
					err = json.Unmarshal(jsonBytes, &rctx)
					if err != nil {
						continue
					}

					crDocID := common.GenerateCombinedHashIDCaseSensitive(rctx.TenantID, rctx.LastCollectedAt, strings.ToLower(rctx.Account), strings.ToLower(rctx.ResourceID), rctx.ResourceType)
					if primaryContext, ok := primaryContext[crDocID]; ok {
						UpdateObjTypeForRctxForDoc(&rctx, primaryContext)
					} else {
						logger.Print(logger.INFO, "Primary context not found", crDocID)
						continue
					}

					resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
					resourceContextInsertDoc, err := json.Marshal(rctx)
					if err != nil {
						logger.Print(logger.ERROR, "Got error marshalling document", err)
						return
					}

					bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
					bulkResourceContextRequest.WriteString("\n")
					bulkResourceContextRequest.Write(resourceContextInsertDoc)
					bulkResourceContextRequest.WriteString("\n")
				}
			}
		}

		if len(bulkResourceContextRequest.String()) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
				return
			}
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful", []string{tenantID})
		bulkResourceContextRequest.Reset()
	}

	time.Sleep(500 * time.Millisecond)
	logger.Print(logger.INFO, "Processing completed for tenant and collectedAt", []string{tenantID, lastCollectedAt})

}

func UpdateObjTypeForRctxForDoc(rctx *common.ResourceContextInsertDoc, primaryContext PrimaryContext) {

	for _, owner := range primaryContext.Owners {

		if owner == "NONE" {
			continue
		}

		if strings.Contains(owner, "<EMAIL>") {
			continue
		}

		for i, rctxOwner := range rctx.ResourceOwnerTypes.DefinedOwners {
			if strings.EqualFold(rctxOwner.Name, owner) {
				rctx.ResourceOwnerTypes.DefinedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}
		}

		for i, rctxOwner := range rctx.ResourceOwnerTypes.DerivedOwners {
			if rctxOwner.Type == common.RESOURCE_NAME_USER_TYPE {
				switch rctx.ResourceType {
				case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE, common.AZURE_USEROWNER_RESOURCE_TYPE:
					continue
				}
			}

			if strings.EqualFold(rctxOwner.Name, owner) {
				rctx.ResourceOwnerTypes.DerivedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}
		}

		for i, rctxOwner := range rctx.ResourceOwnerTypes.CodeOwners {
			if strings.EqualFold(rctxOwner.Name, owner) {
				rctx.ResourceOwnerTypes.CodeOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}
		}

		for i, rctxOwner := range rctx.ResourceOwnerTypes.InheritedOwners {
			if rctx.ResourceType != common.AZURE_RG_RESOURCE_TYPE {
				continue
			}
			if strings.EqualFold(rctxOwner.Name, owner) {
				rctx.ResourceOwnerTypes.InheritedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}
		}

		for i, rctxOwner := range rctx.ResourceOwnerTypes.OpsOwners {
			if rctxOwner.Type != common.RESOURCE_TYPE_OWNER {
				continue
			}

			if strings.EqualFold(rctxOwner.Name, owner) {
				rctx.ResourceOwnerTypes.OpsOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}
		}
	}

	for _, env := range primaryContext.Env {

		for i, rctxEnv := range rctx.ResourceEnvTypes.DefinedEnv {
			if strings.EqualFold(rctxEnv.Name, env) {
				rctx.ResourceEnvTypes.DefinedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxEnv.Desc) == 0 {
				rctx.ResourceEnvTypes.DefinedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
			}
		}

		for i, rctxEnv := range rctx.ResourceEnvTypes.DerivedEnv {
			if strings.EqualFold(rctxEnv.Name, env) {
				rctx.ResourceEnvTypes.DerivedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxEnv.Desc) == 0 {
				rctx.ResourceEnvTypes.DerivedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
			}
		}

		for i, rctxEnv := range rctx.ResourceEnvTypes.InheritedEnv {
			if strings.EqualFold(rctxEnv.Name, env) {
				rctx.ResourceEnvTypes.InheritedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceEnvTypes.InheritedEnv[i].Type = common.INHERITED_COSTCENTER_TYPE
			rctx.ResourceEnvTypes.InheritedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
		}
	}

	for _, app := range primaryContext.App {

		for i, rctxApp := range rctx.ResourceAppTypes.DefinedApp {
			if strings.EqualFold(rctxApp.Name, app) {
				rctx.ResourceAppTypes.DefinedApp[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxApp.Desc) == 0 {
				rctx.ResourceAppTypes.DefinedApp[i].Desc = context.GetStaticDescriptionOfAppType(rctxApp.Type)
			}
		}

		for i, rctxApp := range rctx.ResourceAppTypes.DerivedApp {
			if strings.EqualFold(rctxApp.Name, app) {
				rctx.ResourceAppTypes.DerivedApp[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxApp.Desc) == 0 {
				rctx.ResourceAppTypes.DerivedApp[i].Desc = context.GetStaticDescriptionOfAppType(rctxApp.Type)
			}
		}
	}

	for _, team := range primaryContext.Team {

		for i, rctxTeam := range rctx.ResourceTeamTypes.DefinedTeam {
			if strings.EqualFold(rctxTeam.Name, team) {
				rctx.ResourceTeamTypes.DefinedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxTeam.Desc) == 0 {
				rctx.ResourceTeamTypes.DefinedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)
			}
		}

		for i, rctxTeam := range rctx.ResourceTeamTypes.DerivedTeam {
			if strings.EqualFold(rctxTeam.Name, team) {
				rctx.ResourceTeamTypes.DerivedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxTeam.Desc) == 0 {
				rctx.ResourceTeamTypes.DerivedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)
			}
		}

		for i, rctxTeam := range rctx.ResourceTeamTypes.InheritedTeam {
			if strings.EqualFold(rctxTeam.Name, team) {
				rctx.ResourceTeamTypes.InheritedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceTeamTypes.InheritedTeam[i].Type = common.INHERITED_COSTCENTER_TYPE
			rctx.ResourceTeamTypes.InheritedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)

		}
	}

	for _, software := range primaryContext.Software {

		for i, rctxSoftware := range rctx.ResourceSoftwareTypes.DefinedSoftware {
			if strings.EqualFold(rctxSoftware.Name, software) {
				rctx.ResourceSoftwareTypes.DefinedSoftware[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxSoftware.Desc) == 0 {
				rctx.ResourceSoftwareTypes.DefinedSoftware[i].Desc = context.GetStaticDescriptionOfSoftwareType(rctxSoftware.Type)
			}
		}

		for i, rctxSoftware := range rctx.ResourceSoftwareTypes.DerivedSoftware {
			if strings.EqualFold(rctxSoftware.Name, software) {
				rctx.ResourceSoftwareTypes.DerivedSoftware[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)

			}

			if len(rctxSoftware.Desc) == 0 {
				rctx.ResourceSoftwareTypes.DerivedSoftware[i].Desc = context.GetStaticDescriptionOfSoftwareType(rctxSoftware.Type)
			}
		}
	}

	for _, deployment := range primaryContext.Deployment {

		for i, rctxDeployment := range rctx.ResourceDeploymentTypes.DefinedDeployment {
			if strings.EqualFold(rctxDeployment.Name, deployment) {
				rctx.ResourceDeploymentTypes.DefinedDeployment[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxDeployment.Desc) == 0 {
				rctx.ResourceDeploymentTypes.DefinedDeployment[i].Desc = context.GetStaticDescriptionOfDeploymentType(rctxDeployment.Type)
			}
		}

		for i, rctxDeployment := range rctx.ResourceDeploymentTypes.DerivedDeployment {
			if strings.EqualFold(rctxDeployment.Name, deployment) {
				rctx.ResourceDeploymentTypes.DerivedDeployment[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxDeployment.Desc) == 0 {
				rctx.ResourceDeploymentTypes.DerivedDeployment[i].Desc = context.GetStaticDescriptionOfDeploymentType(rctxDeployment.Type)
			}
		}
	}

	for _, sensitivity := range primaryContext.Sensitivity {

		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.DefinedSensitivity {
			if strings.EqualFold(rctxSensitivity.Name, sensitivity) {
				rctx.ResourceSensitivityTypes.DefinedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxSensitivity.Desc) == 0 {
				rctx.ResourceSensitivityTypes.DefinedSensitivity[i].Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
			}
		}

		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.DerivedSensitivity {
			if strings.EqualFold(rctxSensitivity.Name, sensitivity) {
				rctx.ResourceSensitivityTypes.DerivedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxSensitivity.Desc) == 0 {
				rctx.ResourceSensitivityTypes.DerivedSensitivity[i].Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
			}
		}

		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.InheritedSensitivity {
			if strings.EqualFold(rctxSensitivity.Name, sensitivity) {
				rctx.ResourceSensitivityTypes.InheritedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceSensitivityTypes.InheritedSensitivity[i].Type = common.INHERITED_SENSITIVITY_TYPE
			rctx.ResourceSensitivityTypes.InheritedSensitivity[i].Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
		}
	}

	for _, compliance := range primaryContext.Compliance {

		for i, rctxCompliance := range rctx.ResourceComplianceTypes.DefinedCompliance {
			if strings.EqualFold(rctxCompliance.Name, compliance) {
				rctx.ResourceComplianceTypes.DefinedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxCompliance.Desc) == 0 {
				rctxCompliance.Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
			}
		}

		for i, rctxCompliance := range rctx.ResourceComplianceTypes.DerivedCompliance {
			if strings.EqualFold(rctxCompliance.Name, compliance) {
				rctx.ResourceComplianceTypes.DerivedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxCompliance.Desc) == 0 {
				rctxCompliance.Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
			}
		}

		for i, rctxCompliance := range rctx.ResourceComplianceTypes.InheritedCompliance {
			if strings.EqualFold(rctxCompliance.Name, compliance) {
				rctx.ResourceComplianceTypes.InheritedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceComplianceTypes.InheritedCompliance[i].Type = common.INHERITED_COMPLIANCE_TYPE
			rctx.ResourceComplianceTypes.InheritedCompliance[i].Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
		}
	}

	for _, costCenter := range primaryContext.CostCenter {

		for i, rctxCostCenter := range rctx.ResourceCostCenterTypes.DefinedCostCenter {
			if strings.EqualFold(rctxCostCenter.Name, costCenter) {
				rctx.ResourceCostCenterTypes.DefinedCostCenter[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxCostCenter.Desc) == 0 {
				rctx.ResourceCostCenterTypes.DefinedCostCenter[i].Desc = context.GetStaticDescriptionOfCostCenterType(rctxCostCenter.Type)
			}
		}

		for i, rctxCostCenter := range rctx.ResourceCostCenterTypes.InheritedCostCenter {
			if strings.EqualFold(rctxCostCenter.Name, costCenter) {
				rctx.ResourceCostCenterTypes.InheritedCostCenter[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceCostCenterTypes.InheritedCostCenter[i].Type = common.INHERITED_COSTCENTER_TYPE
			rctx.ResourceCostCenterTypes.InheritedCostCenter[i].Desc = context.GetStaticDescriptionOfCostCenterType(rctxCostCenter.Type)
		}
	}

	for _, ttl := range primaryContext.TTL {

		for i, rctxTTL := range rctx.ResourceTTLTypes.DefinedTTL {
			if strings.EqualFold(rctxTTL.Name, ttl) {
				rctx.ResourceTTLTypes.DefinedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxTTL.Desc) == 0 {
				rctx.ResourceTTLTypes.DefinedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
			}
		}

		for i, rctxTTL := range rctx.ResourceTTLTypes.DerivedTTL {
			if strings.EqualFold(rctxTTL.Name, ttl) {
				rctx.ResourceTTLTypes.DerivedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			if len(rctxTTL.Desc) == 0 {
				rctx.ResourceTTLTypes.DerivedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
			}
		}

		for i, rctxTTL := range rctx.ResourceTTLTypes.InheritedTTL {
			if strings.EqualFold(rctxTTL.Name, ttl) {
				rctx.ResourceTTLTypes.InheritedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			}

			rctx.ResourceTTLTypes.InheritedTTL[i].Type = common.INHERITED_TTL_TYPE
			rctx.ResourceTTLTypes.InheritedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
		}
	}
}
