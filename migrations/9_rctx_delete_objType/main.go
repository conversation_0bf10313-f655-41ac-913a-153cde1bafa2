package main

import (
	"encoding/json"
	"flag"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)

	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	gracefullyShutDown()

	UptadeObjTypeForRctx()

	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}

func UptadeObjTypeForRctx() {
	var (
		searchAfter                any
		bulkResourceContextRequest strings.Builder
	)

	for {
		rctxQuery := `{"query":{"match_all":{}}}`
		rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
		if err != nil {
			return
		}

		if len(rctxDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resource context fetched", len(rctxDocs))

		for docID, rctxDoc := range rctxDocs {

			var rctx common.ResourceContextInsertDoc
			jsonBytes, er := json.Marshal(rctxDoc)
			if er != nil {
				continue
			}
			err = json.Unmarshal(jsonBytes, &rctx)
			if err != nil {
				continue
			}

			nullifyObjectTypes(&rctx)

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(rctx)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")
		}

		if len(bulkResourceContextRequest.String()) > 0 {
			if err := elastic.BulkDocumentsAPI("", elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
				return
			}
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful", []string{""})
		bulkResourceContextRequest.Reset()
	}
}

func nullifyObjectTypes(rctx *common.ResourceContextInsertDoc) {

	processItems := func(items []common.ResourceContextItem) []common.ResourceContextItem {
		for i := range items {
			if items[i].ObjectType != nil {
				items[i].ObjectType = nil
			}
		}
		return items
	}

	// Process ResourceOwnerTypes
	rctx.DefinedOwners = processItems(rctx.DefinedOwners)
	rctx.DerivedOwners = processItems(rctx.DerivedOwners)
	rctx.InheritedOwners = processItems(rctx.InheritedOwners)
	rctx.CodeOwners = processItems(rctx.CodeOwners)
	rctx.CostOwners = processItems(rctx.CostOwners)
	rctx.SecurityOwners = processItems(rctx.SecurityOwners)
	rctx.OpsOwners = processItems(rctx.OpsOwners)

	// Process ResourceEnvTypes
	rctx.DefinedEnv = processItems(rctx.DefinedEnv)
	rctx.DerivedEnv = processItems(rctx.DerivedEnv)
	rctx.InheritedEnv = processItems(rctx.InheritedEnv)

	// Process ResourceAppTypes
	rctx.DefinedApp = processItems(rctx.DefinedApp)
	rctx.DerivedApp = processItems(rctx.DerivedApp)

	// Process ResourceSoftwareTypes
	rctx.DefinedSoftware = processItems(rctx.DefinedSoftware)
	rctx.DerivedSoftware = processItems(rctx.DerivedSoftware)

	// Process ResourceDeploymentTypes
	rctx.DefinedDeployment = processItems(rctx.DefinedDeployment)
	rctx.DerivedDeployment = processItems(rctx.DerivedDeployment)

	// Process ResourceSensitivityTypes
	rctx.DefinedSensitivity = processItems(rctx.DefinedSensitivity)
	rctx.DerivedSensitivity = processItems(rctx.DerivedSensitivity)
	rctx.InheritedSensitivity = processItems(rctx.InheritedSensitivity)

	// Process ResourceComplianceTypes
	rctx.DefinedCompliance = processItems(rctx.DefinedCompliance)
	rctx.DerivedCompliance = processItems(rctx.DerivedCompliance)
	rctx.InheritedCompliance = processItems(rctx.InheritedCompliance)

	// Process ResourceCostCenterTypes
	rctx.DefinedCostCenter = processItems(rctx.DefinedCostCenter)
	rctx.InheritedCostCenter = processItems(rctx.InheritedCostCenter)

	// Process ResourceTeamTypes
	rctx.DefinedTeam = processItems(rctx.DefinedTeam)
	rctx.DerivedTeam = processItems(rctx.DerivedTeam)
	rctx.InheritedTeam = processItems(rctx.InheritedTeam)

	// Process ResourceTTLTypes
	rctx.DefinedTTL = processItems(rctx.DefinedTTL)
	rctx.DerivedTTL = processItems(rctx.DerivedTTL)
	rctx.InheritedTTL = processItems(rctx.InheritedTTL)
}
