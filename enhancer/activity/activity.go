package activity

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	emailutils "github.com/precize/enhancer/internal/email"
	identityutils "github.com/precize/enhancer/internal/identity"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

type ActivityEventsAgg struct {
	ByResource AggsByResource `json:"byResource"`
}

type AggsByResource struct {
	Buckets []struct {
		Key          string           `json:"key"`
		BySourceApp  AggsBySourceApp  `json:"bySourceApp"`
		ByUser       AggsByUser       `json:"byUser"`
		ByFirstEvent AggsByFirstEvent `json:"byFirstEvent"`
	} `json:"buckets"`
}

type AggsBySourceApp struct {
	Buckets []struct {
		Key string `json:"key"`
	} `json:"buckets"`
}

type AggsByUser struct {
	Buckets []struct {
		Key        string `json:"key"`
		MatchedDoc struct {
			Hits struct {
				Hits []struct {
					Source struct {
						CloudtrailEvent string `json:"cloudTrailEvent"`
						EventTime       string `json:"eventTime"`
						EventName       string `json:"eventName"`
						Region          string `json:"region"`
						AccountID       string `json:"accountId"`
					} `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		} `json:"matchedDoc"`

		ByPrincipal AggsByPrincipal `json:"byPrincipal"`
	} `json:"buckets"`
}

type AggsByPrincipal struct {
	Buckets []struct {
		Key        string `json:"key"`
		MatchedDoc struct {
			Hits struct {
				Hits []struct {
					Source struct {
						AccessKeyPrincipalType      string `json:"accessKeyPrincipalType"`
						AccessKeyPrincipalAccountID string `json:"accessKeyPrincipalAccountId"`
						EventTime                   string `json:"eventTime"`
						EventName                   string `json:"eventName"`
						Region                      string `json:"region"`
					} `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		} `json:"matchedDoc"`
	} `json:"buckets"`
}

type AggsByFirstEvent struct {
	Hits struct {
		Hits []struct {
			Source struct {
				Username                    string `json:"username"`
				SourceApp                   string `json:"sourceApp"`
				CloudtrailEvent             string `json:"cloudTrailEvent"`
				EventTime                   string `json:"eventTime"`
				EventName                   string `json:"eventName"`
				Region                      string `json:"region"`
				AccessKeyPrincipalType      string `json:"accessKeyPrincipalType"`
				AccessKeyPrincipalAccountID string `json:"accessKeyPrincipalAccountId"`
			} `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

type EventInfo struct {
	Event, EventTime, Username, EventJSONString, Region, Account, DocID string
}

func GetActivityContextOfResource(resourceContext *rcontext.ResourceContext, contextDocIDs []string) (err error) {

	var (
		resourceIDToDocMap = make(map[string][]string)
		resourceIDs        []string
	)

	for _, resourceDocID := range contextDocIDs {
		if rctxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(resourceDocID); ok {
			resourceID := rctxInsertDoc.ResourceID
			resourceIDToDocMap[resourceID] = append(resourceIDToDocMap[resourceID], resourceDocID)

			// Add escape characters if '\' are present
			if strings.Contains(resourceID, `\`) {
				resourceID = common.EscapeString(resourceID)
			}

			resourceIDs = append(resourceIDs, resourceID)
		}
	}

	aggregatedEventsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"isUpdateEvent":"true"}},{"match":{"eventStatus":"1"}},{"terms":{"resources.resourceName.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}},{"term":{"readOnly":"false"}}]}},"from":0,"size":0,"aggs":{"byResource":{"terms":{"field":"resources.resourceName.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"byUser":{"terms":{"field":"username.keyword","size":100,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"byPrincipal":{"terms":{"field":"accessKeyPrincipalId.keyword","size":100,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"matchedDoc":{"top_hits":{"size":1,"sort":{"eventTime":{"order":"desc"}},"_source":["accessKeyPrincipalType","accessKeyPrincipalAccountId","eventTime","region","eventName"]}}}},"matchedDoc":{"top_hits":{"size":1,"sort":{"eventTime":{"order":"desc"}},"_source":["cloudTrailEvent","eventTime","region","eventName","accountId"]}}}},"bySourceApp":{"terms":{"field":"sourceApp.keyword","size":100,"order":[{"_count":"desc"},{"_key":"asc"}]}},"byFirstEvent":{"top_hits":{"size":1,"sort":[{"eventTime":{"order":"asc"}}],"_source":["username","sourceApp","eventName","cloudTrailEvent","region","eventTime","accessKeyPrincipalType","accessKeyPrincipalAccountId","eventTime"]}}}}}}`

	eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_ACTIVITY_INDEX}, aggregatedEventsQuery)
	if err != nil {
		return
	}

	eventsAggBytes, err := json.Marshal(eventsAggregation)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling", []string{resourceContext.TenantID}, err)
		return
	}

	var activityEventsAgg ActivityEventsAgg
	if err = json.Unmarshal(eventsAggBytes, &activityEventsAgg); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
		return
	}

	for _, resourceBucket := range activityEventsAgg.ByResource.Buckets {

		resourceID := resourceBucket.Key

		// Resources not part of resourceIDs can also come here as events can have multiple resources
		// (of which just one needs to be a resource in resourceIDs)

		// Today, some resources have same resourceId. Hence it is an array

		for _, resourceDocID := range resourceIDToDocMap[resourceID] {

			var (
				activityUsers    = make(map[string]common.ResourceContextItem)
				uniqueUserAgents = make(map[string]struct{})
				creator          = make(map[string]common.ResourceContextItem)
			)

			resourceContextInsertDoc, _ := resourceContext.GetResourceContextInsertDoc(resourceDocID)

			for _, firstEventHit := range resourceBucket.ByFirstEvent.Hits.Hits {

				username := firstEventHit.Source.Username
				sourceApp := firstEventHit.Source.SourceApp
				eventName := firstEventHit.Source.EventName
				eventJSONString := firstEventHit.Source.CloudtrailEvent
				eventTime := firstEventHit.Source.EventTime
				region := firstEventHit.Source.Region
				principalType := firstEventHit.Source.AccessKeyPrincipalType
				principalAccountID := firstEventHit.Source.AccessKeyPrincipalAccountID

				if isCreateEvent(eventName) {
					desc := "Identity has created the resource"

					actor := ""
					switch resourceContextInsertDoc.ServiceID {
					case common.AWS_SERVICE_ID_INT:
						if _, err := common.ParseAddress(username); err != nil {
							actor = getAWSEventActor(principalType, &username, eventJSONString, principalAccountID, resourceContext)
						}
					case common.AZURE_SERVICE_ID_INT:
						if _, err := common.ParseAddress(username); err != nil {
							actor = getAzureEventActor(eventJSONString, resourceContext, &username)
						}
					case common.GCP_SERVICE_ID_INT:
						if common.IsServiceAccountEmail(username) {
							actor = getGCPEventActor(eventJSONString, &username)
						}
					}
					if len(actor) > 0 {
						desc = actor + " has created the resource"
					}

					rctxItem := context.GetUserContextItem(resourceContext, username, common.ACTIVITY_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
							Name:   eventName,
							Region: region,
							Time:   eventTime,
						},
					)

					rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
					creator[rctxItem.Name] = rctxItem

					if deploymentNames := context.GetDeploymentNamesFromValue(sourceApp); len(deploymentNames) > 0 {

						for _, deploymentName := range deploymentNames {

							resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
								resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
								common.ResourceContextItem{
									Name: deploymentName,
									Type: common.ACTIVITY_DEPLOYMENT_TYPE,
									Desc: context.GetStaticDescriptionOfDeploymentType(common.ACTIVITY_DEPLOYMENT_TYPE),
								},
							)
						}
					}
				}
			}

			userAgents := make([]string, 0)

			for _, sourceAppBucket := range resourceBucket.BySourceApp.Buckets {

				sourceApp := sourceAppBucket.Key

				// if derived deployment is not empty that means it must have been derived during create event which get's more preference
				if len(resourceContextInsertDoc.DerivedDeployment) <= 0 {

					if deploymentNames := context.GetDeploymentNamesFromValue(sourceApp); len(deploymentNames) > 0 {

						for _, deploymentName := range deploymentNames {

							resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
								resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
								common.ResourceContextItem{
									Name: deploymentName,
									Type: common.ACTIVITY_DEPLOYMENT_TYPE,
									Desc: context.GetStaticDescriptionOfDeploymentType(common.ACTIVITY_DEPLOYMENT_TYPE),
								},
							)
						}
					}
				}

				if agents := context.GetUserAgentCategoriesFromValue(sourceApp); len(agents) > 0 {
					for _, userAgent := range agents {
						if _, ok := uniqueUserAgents[userAgent]; !ok && len(userAgent) > 0 {
							uniqueUserAgents[userAgent] = struct{}{}
							userAgents = append(userAgents, userAgent)
						}
					}
				} else if len(sourceApp) > 0 {
					if _, ok := uniqueUserAgents[sourceApp]; !ok && len(sourceApp) > 0 {
						uniqueUserAgents[sourceApp] = struct{}{}
						userAgents = append(userAgents, sourceApp)
					}
				}
			}

			resourceContextInsertDoc.UserAgents = append([]string{}, userAgents...)

			for _, userBucket := range resourceBucket.ByUser.Buckets {

				username := userBucket.Key

				for _, userInnerHit := range userBucket.MatchedDoc.Hits.Hits {

					eventJSONString := userInnerHit.Source.CloudtrailEvent
					eventTime := userInnerHit.Source.EventTime
					event := userInnerHit.Source.EventName
					region := userInnerHit.Source.Region
					account := userInnerHit.Source.AccountID

					var eventInfo = EventInfo{
						Event:           event,
						EventTime:       eventTime,
						Username:        username,
						EventJSONString: eventJSONString,
						Region:          region,
						Account:         account,
						DocID:           resourceDocID,
					}

					switch resourceContextInsertDoc.ServiceID {

					case common.AWS_SERVICE_ID_INT:

						if _, err := common.ParseAddress(username); err == nil {
							addUserEmailAsActivityUser(username, resourceContext, eventInfo, activityUsers, resourceContextInsertDoc)
						} else {
							ParseAWSActivity(resourceContext, eventInfo, activityUsers, userBucket.ByPrincipal, resourceContextInsertDoc)
						}

					case common.AZURE_SERVICE_ID_INT:

						if _, err := common.ParseAddress(username); err == nil {
							addUserEmailAsActivityUser(username, resourceContext, eventInfo, activityUsers, resourceContextInsertDoc)
						} else {
							ParseAzureActivity(resourceContext, eventInfo, activityUsers, resourceContextInsertDoc)
						}

					case common.GCP_SERVICE_ID_INT:

						if _, err := common.ParseAddress(username); err == nil {
							addUserEmailAsActivityUser(username, resourceContext, eventInfo, activityUsers, resourceContextInsertDoc)
						} else if common.IsServiceAccountEmail(username) {
							// Service Account
							ParseGCPActivity(resourceContext, eventInfo, activityUsers, resourceContextInsertDoc)
						} else {
							logger.Print(logger.INFO, "Unhandled GCP Activity user case", []string{resourceContext.TenantID}, username)
						}
					}
				}
			}

			// Avoid activities done by a person on its own resource (for eg, AD User)
			for k, user := range activityUsers {
				if strings.EqualFold(user.Name, resourceContextInsertDoc.ResourceName) {
					delete(activityUsers, k)
				} else if creatorRctx, ok := creator[user.Name]; ok {
					// if creator is also an activity user, remove the activity user
					user.OwnerSubType = creatorRctx.OwnerSubType
					activityUsers[k] = user
					delete(creator, user.Name)
				}
			}

			eventTimes := make([]string, 0, len(activityUsers))

			for k := range activityUsers {
				eventTimes = append(eventTimes, k)
			}

			sort.Sort(sort.Reverse(sort.StringSlice(eventTimes)))

			for _, eventTime := range eventTimes {
				rctxItem := activityUsers[eventTime]
				if creator, ok := creator[rctxItem.Name]; ok {
					rctxItem.OwnerSubType = creator.OwnerSubType
				}

				identityID := rctxItem.Name
				identityID = common.RemoveSuffixes(identityID, identityutils.NonHumanIdentitySuffixes)
				if parentEmail, ok := resourceContext.GetChildPrimaryEmail(rctxItem.Name); ok {
					identityID = parentEmail
				}

				if addr, err := common.ParseAddress(identityID); err == nil {
					if isActiveIdentity, ok := resourceContext.GetEmailStatus(addr.Address); ok && !isActiveIdentity {

						if eventTime, err := elastic.ParseDateTime(rctxItem.Event.Time); err == nil {

							// EX-EMPLOYEE activity owner with event time before 3 months to be skipped only if there are other activity owners present
							if eventTime.Before(time.Now().AddDate(0, -6, 0)) {
								continue
							}

						}
					}
				}

				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					rctxItem,
				)
			}

			for _, rctxItem := range creator {

				identityID := rctxItem.Name
				identityID = common.RemoveSuffixes(identityID, identityutils.NonHumanIdentitySuffixes)
				if parentEmail, ok := resourceContext.GetChildPrimaryEmail(rctxItem.Name); ok {
					identityID = parentEmail
				}

				if addr, err := common.ParseAddress(identityID); err == nil {
					if isActiveIdentity, ok := resourceContext.GetEmailStatus(addr.Address); ok && !isActiveIdentity {

						if eventTime, err := elastic.ParseDateTime(rctxItem.Event.Time); err == nil {

							// EX-EMPLOYEE activity owner with event time before 3 months to be skipped only if there are other activity owners present
							if eventTime.Before(time.Now().AddDate(0, -6, 0)) {
								continue
							}

						}
					}
				}

				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					rctxItem,
				)
			}

			resourceContext.SetResourceContextInsertDoc(resourceDocID, resourceContextInsertDoc)
		}
	}

	return
}

func addUserEmailAsActivityUser(username string, resourceContext *rcontext.ResourceContext, eventInfo EventInfo, activityUsers map[string]common.ResourceContextItem, resourceCtxInsertDoc common.ResourceContextInsertDoc) {

	if emailutils.IsNonHumanEmail(username, resourceContext) {
		uniqueGroupEmailIdentities := make(map[string]struct{})
		GetNonHumanEmailOwnersForActivity(resourceContext, username, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+username+" has performed activities on the resource", "a")
	}

	activityUsers[eventInfo.EventTime] = context.GetUserContextItem(resourceContext, username, common.ACTIVITY_USER_TYPE,
		"User has performed activities on the resource", "", resourceCtxInsertDoc.Account, &common.ResourceCtxEvent{
			Name:   eventInfo.Event,
			Region: eventInfo.Region,
			Time:   eventInfo.EventTime,
		})
}

func getIdentityChainDescription(desc, identity string, depth int) string {
	if depth == 1 {
		desc = strings.ReplaceAll(desc, "User owned", "User was traced back from ")
		desc = strings.ReplaceAll(desc, " has ", " which has ")
		desc += " via the identity chain "
	} else {
		desc += ", "
	}
	return desc + identity
}

func GetNonHumanEmailOwnersForActivity(resourceContext *rcontext.ResourceContext, email string, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, uniqueIdentities map[string]struct{}, desc, sortAlphabet string) {

	if _, ok := uniqueIdentities[email]; ok {
		return
	}

	uniqueIdentities[email] = struct{}{}
	depth := len(sortAlphabet)

	if userResource, ok := resourceContext.GetUserResource(email); ok {

		for _, source := range userResource.Sources {

			// Only AD and Okta comes here

			docID := common.GenerateCombinedHashID(source.ResourceID, source.IdentityType, source.AccountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if doc, ok := resourceContext.GetResourceContextInsertDoc(docID); ok {

				for i, defined := range doc.DefinedOwners {
					if strings.HasSuffix(defined.Name, contextutils.APP_USER_SUFFIX) {
						if depth <= 5 && len(defined.IdentityId) > 0 {
							for _, azureTenantID := range resourceContext.AzureTenantIDs {
								GetApplicationOwnersForActivity(resourceContext, defined.IdentityId, azureTenantID, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), sortAlphabet+sortAlphabet, doc)
							}
						}
					} else if emailutils.IsNonHumanEmail(defined.Name, resourceContext) {
						GetNonHumanEmailOwnersForActivity(resourceContext, defined.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), sortAlphabet+sortAlphabet)
					} else {
						if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
							uniqueIdentities[defined.Name] = struct{}{}
							activityUsers[eventInfo.EventTime+sortAlphabet+"b"+fmt.Sprintf("%03d", len(doc.DefinedOwners)-i)] = context.GetUserContextItem(resourceContext, defined.Name, common.ACTIVITY_USER_TYPE,
								desc, "", doc.Account, &common.ResourceCtxEvent{
									Name:          eventInfo.Event,
									Region:        eventInfo.Region,
									Time:          eventInfo.EventTime,
									IndirectEvent: true,
								})
							break
						}
					}
				}

				for i, derived := range doc.DerivedOwners {
					if strings.HasSuffix(derived.Name, contextutils.APP_USER_SUFFIX) {
						if depth <= 5 && len(derived.IdentityId) > 0 {
							for _, azureTenantID := range resourceContext.AzureTenantIDs {
								GetApplicationOwnersForActivity(resourceContext, derived.IdentityId, azureTenantID, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), sortAlphabet+sortAlphabet, doc)
							}
						}
					} else if emailutils.IsNonHumanEmail(derived.Name, resourceContext) {
						GetNonHumanEmailOwnersForActivity(resourceContext, derived.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), sortAlphabet+sortAlphabet)
					} else {
						if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
							uniqueIdentities[derived.Name] = struct{}{}
							activityUsers[eventInfo.EventTime+sortAlphabet+"a"+fmt.Sprintf("%03d", len(doc.DerivedOwners)-i)] = context.GetUserContextItem(resourceContext, derived.Name, common.ACTIVITY_USER_TYPE,
								desc, "", doc.Account, &common.ResourceCtxEvent{
									Name:          eventInfo.Event,
									Region:        eventInfo.Region,
									Time:          eventInfo.EventTime,
									IndirectEvent: true,
								})
							break
						}
					}
				}
			}
		}
	}
}

func GetNonHumanEmailOwners(resourceContext *rcontext.ResourceContext, email string, resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueIdentities map[string]struct{}, desc string, depth int) {

	if _, ok := uniqueIdentities[email]; ok {
		return
	}

	uniqueIdentities[email] = struct{}{}

	if userResource, ok := resourceContext.GetUserResource(email); ok {

		for _, source := range userResource.Sources {

			// Only AD and Okta comes here

			docID := common.GenerateCombinedHashID(source.ResourceID, source.IdentityType, source.AccountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if doc, ok := resourceContext.GetResourceContextInsertDoc(docID); ok {

				for _, defined := range doc.DefinedOwners {
					if strings.HasSuffix(defined.Name, contextutils.APP_USER_SUFFIX) {
						if depth <= 5 && len(defined.IdentityId) > 0 {
							for _, azureTenantID := range resourceContext.AzureTenantIDs {
								GetApplicationOwnersForPolicy(resourceContext, defined.IdentityId, azureTenantID, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), depth+1)
							}
						}
					} else if emailutils.IsNonHumanEmail(defined.Name, resourceContext) {
						if len(defined.IdentityId) > 0 && depth <= 5 {
							// Recursive call
							GetNonHumanEmailOwners(resourceContext, defined.IdentityId, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), depth+1)
						}
					} else {
						if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
							uniqueIdentities[defined.Name] = struct{}{}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, defined.Name, common.POLICYBINDING_USER_TYPE,
									desc, "", resourceContextInsertDoc.Account, nil))
							break
						}
					}
				}

				for _, derived := range doc.DerivedOwners {
					if strings.HasSuffix(derived.Name, contextutils.APP_USER_SUFFIX) {
						if depth <= 5 && len(derived.IdentityId) > 0 {
							for _, azureTenantID := range resourceContext.AzureTenantIDs {
								GetApplicationOwnersForPolicy(resourceContext, derived.IdentityId, azureTenantID, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), depth+1)
							}
						}
					} else if emailutils.IsNonHumanEmail(derived.Name, resourceContext) {
						if len(derived.IdentityId) > 0 && depth <= 5 {
							// Recursive call
							GetNonHumanEmailOwners(resourceContext, derived.IdentityId, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), depth+1)
						}
					} else {
						if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
							uniqueIdentities[derived.Name] = struct{}{}
							if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
								uniqueIdentities[derived.Name] = struct{}{}
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, derived.Name, common.POLICYBINDING_USER_TYPE,
										desc, "", resourceContextInsertDoc.Account, nil))

								break
							}
						}
					}
				}
			}
		}
	}
}
