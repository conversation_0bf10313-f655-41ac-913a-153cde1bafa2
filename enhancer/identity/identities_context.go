package identity

import (
	"encoding/json"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	emailutils "github.com/precize/enhancer/internal/email"
	identityutils "github.com/precize/enhancer/internal/identity"
	"github.com/precize/enhancer/internal/sddl"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

const (
	ID_TYPE    = "id"
	EMAIL_TYPE = "email"
)

type UserMaps struct {
	orgMap                 map[string][]string
	emailNameMap           map[string]string
	identitySDDLPrimaryMap map[string]string // store the mapping of parent and child identities for sddl identities
}

func GetUsers(resourceContext *rcontext.ResourceContext) {

	logger.Print(logger.INFO, "Gathering cloud users", []string{resourceContext.TenantID})

	var (
		userMaps = UserMaps{
			orgMap:                 make(map[string][]string),
			emailNameMap:           make(map[string]string),
			identitySDDLPrimaryMap: make(map[string]string),
		}
	)

	getCloudUsers(resourceContext, &userMaps)

	if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
		getOktaUsers(resourceContext, &userMaps)
	}

	postProcessIdentities(resourceContext, &userMaps)

	logger.Print(logger.INFO, "Gathered cloud users", []string{resourceContext.TenantID})
}

func getCloudUsers(resourceContext *rcontext.ResourceContext, userMaps *UserMaps) {

	var (
		searchAfter              any
		cloudUserIdentitiesQuery = `{"_source":["primaryEmail","identityId","identityStatus","name","type","deleted","additionalInfo","accessKeys","accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}}]}}}`
	)

	for {
		cloudUserIdentitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, cloudUserIdentitiesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(cloudUserIdentitiesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, cloudUserIdentitiesDoc := range cloudUserIdentitiesDocs {
			processIdentityDoc(resourceContext.TenantID, cloudUserIdentitiesDoc, resourceContext, userMaps)
		}
	}
}

func processIdentityDoc(tenantID string, cloudUserIdentitiesDoc map[string]any, resourceContext *rcontext.ResourceContext, userMaps *UserMaps) {

	var (
		identitiesDoc    common.IdentitiesDoc
		hasConsoleAccess = true
	)

	identitiesDocBytes, err := json.Marshal(cloudUserIdentitiesDoc)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling identities doc to JSON", []string{tenantID}, err)
		return
	}

	if err = json.Unmarshal(identitiesDocBytes, &identitiesDoc); err != nil {
		logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
		return
	}

	originalIdentityID := identitiesDoc.IdentityID
	identityID := identitiesDoc.IdentityID
	if len(identityID) <= 0 {
		return
	}
	identityID = strings.ToLower(identityID)

	identityType := identitiesDoc.Type
	if len(identityType) <= 0 {
		return
	}

	if isServiceIdentityType(identityType, identitiesDoc) {
		return
	}

	// precize created identities will already have non-human and email status evaluated
	if isPrecizeCreatedIdentity(identityType) {

		if len(identitiesDoc.PrimaryEmail) > 0 {
			if ok := resourceContext.SetChildPrimaryEmail(originalIdentityID, identitiesDoc.PrimaryEmail, false); ok {
				identityID = identitiesDoc.PrimaryEmail
			}
		}

		switch identitiesDoc.IdentityStatus {
		case common.CURRENT_EMPLOYEE_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, false)
		case common.EX_EMPLOYEE_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, false)
		case common.VALID_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, true)
		}
	}

	additionalInfo := make(map[string]any)
	if err := json.Unmarshal([]byte(identitiesDoc.AdditionalInfo), &additionalInfo); err != nil {
		return
	}

	switch identityType {

	case common.AWS_USER_IDENTITY_TYPE:

		// AWS IAM Users without console access are used for programmatic access
		// They should not be considered as a user, but as a service identitity only
		if iamUserHasConsoleAccess, ok := additionalInfo["hasLoginProfile"].(bool); ok && !iamUserHasConsoleAccess {
			hasConsoleAccess = false
		}

	case common.AWS_SSOUSER_IDENTITY_TYPE:

		if emails, ok := additionalInfo["emails"].([]any); ok {
			for _, email := range emails {
				if email, ok := email.(string); ok {
					email = strings.ToLower(email)
					if ok := resourceContext.SetChildPrimaryEmail(identityID, email, true); ok {
						identityID = email
					}
				}
			}
		}
	}

	// Apply typo exceptions
	if vals, ok := resourceContext.GetTypoExceptions(identityID); ok && len(vals) == 1 {
		identityID = vals[0]
	}

	identityName := common.ConvertToTitleCase(identitiesDoc.Name)

	if _, err := common.ParseAddress(identityName); err == nil {
		identityName = common.GetFormattedNameFromEmail(identityName)
	}

	extractIdentityNameFromAdditionalInfo(&identityName, identityType, additionalInfo)

	deleted, _ := determineUserStatus(identitiesDoc)

	isSddl := sddl.HandleIdentitiesWithSDDL(&identityID, &identityName, resourceContext)

	addr, err := common.ParseAddress(identityID)
	if err != nil {
		email := emailutils.DeriveEmailFromName(identityName, resourceContext)
		if addr, err = common.ParseAddress(email); err != nil {
			return
		}

		// without console access iam users are not merged but if email is derived from inclusion list (from exceptions index) forcibly convert console access to true
		if values, ok := resourceContext.GetDerivedEmailInclusions(strings.ToLower(identityName)); ok && len(values) > 0 {
			if slices.Contains(values, email) && !hasConsoleAccess {
				hasConsoleAccess = true
			}
		}

		// user with no email should be merged if derived email is valid
		// Iam users without console access should not be considered as a user and hence should not be merged
		if hasConsoleAccess {
			resourceContext.SetChildPrimaryEmail(originalIdentityID, email, false)
		} else {

			if len(email) > 0 {
				identityName = common.GetFormattedNameFromEmail(email)
			}
			resourceContext.SetChildPrimaryEmail(originalIdentityID, "", false)
		}
	}

	if addr, err := common.ParseAddress(identityName); err == nil {
		identityName = common.GetFormattedNameFromEmail(addr.Address)
	}

	userResource := rcontext.UserContext{
		Name:   identityName,
		Email:  addr.Address,
		Active: !deleted,
		IsSddl: isSddl,
		Type:   map[string]struct{}{identityType: {}},

		Manager:    map[string]string{},
		JobTitle:   map[string]struct{}{},
		Department: map[string]struct{}{},
		Team:       map[string]struct{}{},
		Groups:     map[string]struct{}{},
	}

	if !hasConsoleAccess {
		userResource.SkipUser = true
	}

	updateEmailToNameMapping(resourceContext, addr.Address, identityName)

	addEmailToEmailStatusReq(resourceContext, &userResource, userResource.Active, isSddl, userMaps)

	switch identityType {
	case common.AZURE_AD_USER_IDENTITY_TYPE:
		processAzureADUser(resourceContext, userMaps, identitiesDoc, &userResource, addr.Address)
	case common.OPENAI_USER_IDENTITY_TYPE:
		processOpenAIIdentity(resourceContext, identitiesDoc, &userResource)
	default:
		// Add user to UserResource if not present
		if existingUsrRsc, ok := resourceContext.GetUserResource(addr.Address); !ok {
			resourceContext.SetUserResource(addr.Address, &userResource)
		} else {
			// isSddl flag should be preserved in case of duplicate user resources
			if userResource.IsSddl {
				existingUsrRsc.IsSddl = true
			}

			resourceContext.SetUserResource(addr.Address, existingUsrRsc)
		}
	}
}

func postProcessIdentities(resourceContext *rcontext.ResourceContext, userMaps *UserMaps) {

	var (
		batchSize    = 10
		emailNameMap = make(map[string]string)
		nameList     = make(map[string][]string)
	)

	for email, name := range userMaps.emailNameMap {
		emailNameMap[email] = name
		if len(emailNameMap) >= batchSize {
			err := ProcessBatchEmailValidity(emailNameMap, resourceContext)
			if err != nil {
				emailNameMap = make(map[string]string)
				continue
			}

			emailNameMap = make(map[string]string)
		}
	}

	if len(emailNameMap) > 0 {
		if err := ProcessBatchEmailValidity(emailNameMap, resourceContext); err != nil {
			//skip, if error is returned
		}
	}

	resourceContext.RangeUserResources(func(email string, userResource *rcontext.UserContext) bool {

		temp := userResource

		if fullName, ok := resourceContext.GetEmailToFullName(userResource.Email); ok {
			temp.Name = fullName
		}

		if isActive, ok := resourceContext.GetEmailStatus(temp.Email); ok {
			temp.Active = isActive
		}

		if len(temp.Team) <= 0 && len(temp.Manager) > 0 {

			for managerValue, managerType := range userResource.Manager {

				var managerEmail = managerValue
				if managerType == ID_TYPE {
					managerEmail, _ = resourceContext.GetUserIDToEmail(managerValue)
				}

				if managerUser, ok := resourceContext.GetUserResource(managerEmail); ok {
					for managerTeam := range managerUser.Team {
						temp.Team[managerTeam] = struct{}{}
					}
				}
			}
		}

		if strings.Contains(strings.ToLower(temp.Email), contextutils.EXT_KEYWORD) {
			identityutils.HandleIdentityWithExternalEmail(resourceContext, temp)
		}

		if strings.Contains(strings.ToLower(temp.Email), contextutils.LIVE_MICROSOFT_KEYWORD) || strings.Contains(strings.ToLower(temp.Email), contextutils.MAIL_MICROSOFT_KEYWORD) {
			identityutils.HandlePersonalAndMicrosoftEmail(resourceContext, temp)
		}

		identityutils.HandleChildEmail(resourceContext, temp)

		// Reassigned for ex employees
		if !temp.Active {
			handleExEmployeeReassignment(resourceContext, temp, userMaps)
		}

		if ok := resourceContext.GetInvalidEmailCache(temp.Email); ok {
			temp.IsInvalid = true
		}

		resourceContext.DeleteUserResource(email)
		resourceContext.SetUserResource(temp.Email, temp)

		InitializeHumanOrNonHumanEvaluation(temp.Email, temp, resourceContext, nameList)
		return true
	})

	EvaluateNamesForHumanOrNonHuman(nameList, resourceContext)
}

func ProcessBatchEmailValidity(emailNameMap map[string]string, resourceContext *rcontext.ResourceContext) error {

	emailStatusRes, err := emailutils.CheckEmailValidity(emailNameMap, false, resourceContext)
	if err != nil {
		return err
	}

	for email, status := range emailStatusRes {
		resourceContext.SetEmailStatus(email, status)
	}

	return nil
}
