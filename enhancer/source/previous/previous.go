package previous

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/context"
	emailutils "github.com/precize/enhancer/internal/email"
	identityutils "github.com/precize/enhancer/internal/identity"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func ProcessPreviousContext(resourceContext *rcontext.ResourceContext, contextDocIDs []string) {

	var (
		previousContextDocIDs []string
		prevToCurrentDocIDMap = make(map[string]string)
	)

	if len(resourceContext.PreviousCollectedAt) == 0 {
		return
	}

	for _, currentContextDocID := range contextDocIDs {

		if currentContextDoc, ok := resourceContext.GetResourceContextInsertDoc(currentContextDocID); ok {

			var previousContextDocID string

			switch currentContextDoc.ResourceType {
			case common.AWS_ACCOUNT_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE, common.AWS_ORG_RESOURCE_TYPE, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE:

				previousContextDocID = common.GenerateCombinedHashID(currentContextDoc.ResourceID, currentContextDoc.ResourceType, currentContextDoc.ResourceID, resourceContext.PreviousCollectedAt, resourceContext.TenantID)

			default:

				previousContextDocID = common.GenerateCombinedHashID(currentContextDoc.ResourceID, currentContextDoc.ResourceType, currentContextDoc.Account, resourceContext.PreviousCollectedAt, resourceContext.TenantID)

			}

			if len(previousContextDocID) > 0 {
				previousContextDocIDs = append(previousContextDocIDs, previousContextDocID)
				prevToCurrentDocIDMap[previousContextDocID] = currentContextDocID
			}
		}
	}

	prevContextQuery := `{"query":{"ids":{"values":["` + strings.Join(previousContextDocIDs, `","`) + `"]}}}`

	prevContextDocs, err := elastic.ExecuteSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, prevContextQuery)
	if err != nil {
		return
	}

	for _, prevContextDoc := range prevContextDocs {

		prevContextDocBytes, err := json.Marshal(prevContextDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{resourceContext.TenantID}, err)
			return
		}

		var previousContextDoc common.ResourceContextInsertDoc

		if err = json.Unmarshal(prevContextDocBytes, &previousContextDoc); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
			return
		}

		currentContextDocID := prevToCurrentDocIDMap[previousContextDoc.ID]

		if currentContextDoc, ok := resourceContext.GetResourceContextInsertDoc(currentContextDocID); ok {
			ProcessPreviousContextForOwners(resourceContext, &currentContextDoc, previousContextDoc)
			ProcessPreviousContextForApplications(resourceContext, &currentContextDoc, previousContextDoc)
			ProcessPreviousContextForDeployments(resourceContext, &currentContextDoc, previousContextDoc)
			ProcessPreviousContextForUserAgents(&currentContextDoc, previousContextDoc)
			resourceContext.SetResourceContextInsertDoc(currentContextDocID, currentContextDoc)
		}
	}
}

func ProcessPreviousContextForOwners(resourceContext *rcontext.ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	activityUsers := make(map[string]common.ResourceContextItem)

	for i, prevDerivedOwner := range previousContextDoc.ResourceOwnerTypes.DerivedOwners {

		if prevDerivedOwner.Type == common.ACTIVITY_USER_TYPE || prevDerivedOwner.Type == common.CREATOR_USER_TYPE {

			if prevDerivedOwner.Event != nil && !prevDerivedOwner.Event.IndirectEvent {

				var present bool

				for _, currentDerivedOwner := range currentContextDoc.ResourceOwnerTypes.DerivedOwners {

					if currentDerivedOwner.Type == common.ACTIVITY_USER_TYPE || currentDerivedOwner.Type == common.CREATOR_USER_TYPE {

						if strings.ToLower(currentDerivedOwner.Name) == strings.ToLower(prevDerivedOwner.Name) || currentDerivedOwner.IdentityId == prevDerivedOwner.IdentityId {
							present = true
							break
						} else {
							if prevOwnerEmail, err := common.ParseAddress(prevDerivedOwner.Name); err == nil {
								if currentOwnerEmail, err := common.ParseAddress(currentDerivedOwner.Name); err == nil {
									if prevOwnerEmail.Address == currentOwnerEmail.Address {
										present = true
										break
									}
								}
							}
						}
					}
				}

				if !present {

					var (
						uniqueIdentities = make(map[string]struct{})
						eventInfo        = activity.EventInfo{
							Event:     prevDerivedOwner.Event.Name,
							Region:    prevDerivedOwner.Event.Region,
							EventTime: prevDerivedOwner.Event.Time,
						}
					)

					prevDerivedOwner.Name = strings.TrimPrefix(prevDerivedOwner.Name, contextutils.EX_EMPLOYEE_PREFIX)
					prevDerivedOwner.Name = strings.TrimPrefix(prevDerivedOwner.Name, contextutils.INVALID_EMPLOYEE_PREFIX)

					if strings.HasSuffix(prevDerivedOwner.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX)
						activity.GetServiceAccountOwnersForActivity(resourceContext, name, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+name+" has performed activities on the resource", "b", *currentContextDoc)
					} else if strings.HasSuffix(prevDerivedOwner.Name, contextutils.IAM_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, contextutils.IAM_USER_SUFFIX)
						activity.GetAWSIAMEntityOwnersForActivity(resourceContext, name, eventInfo, activityUsers, prevDerivedOwner.Event.IdentityAccount, common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, "User owned IAM User "+name+" has performed activities on the resource", "b")
					} else if strings.HasSuffix(prevDerivedOwner.Name, contextutils.IAM_ROLE_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, contextutils.IAM_ROLE_SUFFIX)
						activity.GetAWSIAMEntityOwnersForActivity(resourceContext, name, eventInfo, activityUsers, prevDerivedOwner.Event.IdentityAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, "User owned IAM Role "+name+" has performed activities on the resource", "b")
					} else if strings.HasSuffix(prevDerivedOwner.Name, contextutils.APP_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, contextutils.APP_USER_SUFFIX)
						activity.GetApplicationOwnersForActivity(resourceContext, name, prevDerivedOwner.Event.IdentityAccount, eventInfo, activityUsers, uniqueIdentities, "User owned Application "+name+" has performed activities on the resource", "b", *currentContextDoc)
					} else if emailutils.IsNonHumanEmail(prevDerivedOwner.Name, resourceContext) {
						activity.GetNonHumanEmailOwnersForActivity(resourceContext, prevDerivedOwner.Name, eventInfo, activityUsers, uniqueIdentities, "User owned group email "+prevDerivedOwner.Name+" has performed activities on the resource", "b")
					}

					activityUsers[eventInfo.EventTime+"a"+fmt.Sprintf("%03d", len(previousContextDoc.DerivedOwners)-i)] = context.GetUserContextItem(resourceContext, prevDerivedOwner.Name, prevDerivedOwner.Type, prevDerivedOwner.Desc, prevDerivedOwner.IdentityId, currentContextDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.Event,
						Region: eventInfo.Region,
						Time:   eventInfo.EventTime,
					})
				}
			}
		}
	}

	eventTimes := make([]string, 0, len(activityUsers))

	for k := range activityUsers {
		eventTimes = append(eventTimes, k)
	}

	sort.Sort(sort.Reverse(sort.StringSlice(eventTimes)))

	for _, eventTime := range eventTimes {

		activityUser := activityUsers[eventTime]

		activityUser.CollectedFrom = resourceContext.PreviousCollectedAt

		identityID := activityUser.Name
		identityID = common.RemoveSuffixes(identityID, identityutils.NonHumanIdentitySuffixes)
		if parentEmail, ok := resourceContext.GetChildPrimaryEmail(activityUser.Name); ok {
			identityID = parentEmail
		}

		if addr, err := common.ParseAddress(identityID); err == nil {
			if isActiveIdentity, ok := resourceContext.GetEmailStatus(addr.Address); ok && !isActiveIdentity {

				if eventTime, err := elastic.ParseDateTime(activityUser.Event.Time); err == nil {

					// EX-EMPLOYEE activity owner with event time before 3 months to be skipped only if there are other activity owners present
					if eventTime.Before(time.Now().AddDate(0, -6, 0)) {
						continue
					}
				}
			}
		}

		currentContextDoc.ResourceOwnerTypes.DerivedOwners = append(currentContextDoc.ResourceOwnerTypes.DerivedOwners, activityUser)
	}
}

func ProcessPreviousContextForDeployments(resourceContext *rcontext.ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevDerivedDeployment := range previousContextDoc.ResourceDeploymentTypes.DerivedDeployment {

		if prevDerivedDeployment.Type == common.ACTIVITY_DEPLOYMENT_TYPE {

			var present bool

			for _, currentDerivedDeployment := range currentContextDoc.ResourceDeploymentTypes.DerivedDeployment {
				if strings.ToLower(currentDerivedDeployment.Name) == strings.ToLower(prevDerivedDeployment.Name) {
					present = true
					break
				}
			}

			if !present {
				currentContextDoc.ResourceDeploymentTypes.DerivedDeployment = append(currentContextDoc.ResourceDeploymentTypes.DerivedDeployment, common.ResourceContextItem{
					Name:          prevDerivedDeployment.Name,
					Type:          prevDerivedDeployment.Type,
					Desc:          prevDerivedDeployment.Desc,
					CollectedFrom: resourceContext.PreviousCollectedAt,
				})
			}
		}
	}
}

func ProcessPreviousContextForApplications(resourceContext *rcontext.ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevDerivedApp := range previousContextDoc.ResourceAppTypes.DerivedApp {

		if prevDerivedApp.Type == common.ACTIVITY_APP_TYPE {

			var present bool

			for _, currentDerivedApp := range currentContextDoc.ResourceAppTypes.DerivedApp {
				if strings.ToLower(currentDerivedApp.Name) == strings.ToLower(prevDerivedApp.Name) {
					present = true
					break
				}
			}

			if !present {
				currentContextDoc.ResourceAppTypes.DerivedApp = append(currentContextDoc.ResourceAppTypes.DerivedApp, common.ResourceContextItem{
					Name:          prevDerivedApp.Name,
					Type:          prevDerivedApp.Type,
					Desc:          prevDerivedApp.Desc,
					CollectedFrom: resourceContext.PreviousCollectedAt,
				})
			}
		}
	}
}

func ProcessPreviousContextForUserAgents(currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevUserAgent := range previousContextDoc.UserAgents {

		var present bool

		for _, currentUserAgent := range currentContextDoc.UserAgents {
			if strings.ToLower(currentUserAgent) == strings.ToLower(prevUserAgent) {
				present = true
				break
			}
		}

		if !present {
			currentContextDoc.UserAgents = append(currentContextDoc.UserAgents, prevUserAgent)
		}
	}
}
