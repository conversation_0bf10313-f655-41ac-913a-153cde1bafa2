package code

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

type CodeContextBucket struct {
	Key    string `json:"key"`
	ByUser struct {
		Buckets []Bucket `json:"buckets"`
	} `json:"byUser"`
	ByCommitDocId struct {
		Buckets []Bucket `json:"buckets"`
	} `json:"byCommitDocId"`
}

type Bucket struct {
	Key        string     `json:"key"`
	MatchedDoc MatchedDoc `json:"matchedDoc"`
}

type MatchedDoc struct {
	Hits struct {
		Hits []struct {
			Source CodeContextSource `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

type CodeContextSource struct {
	UserType       string `json:"userType"`
	PriorityConfig string `json:"priorityConfigs"`
	DerivedFrom    string `json:"derivedFrom"`
}

type CommitFileAggregation struct {
	Buckets []CommitFileBucket `json:"buckets"`
}

type CommitFileBucket struct {
	Key             string          `json:"key"` // filename
	RepoAggregation RepoAggregation `json:"repo_aggregation"`
}

type RepoAggregation struct {
	Buckets []Bucket `json:"buckets"`
}

func GetCodeContextOfResource(resourceContext *rcontext.ResourceContext, resourceContextDocIDs []string) (err error) {

	var (
		resourceIDToDocMap = make(map[string][]string)
		resourceIDs        []string
	)

	for _, resourceDocID := range resourceContextDocIDs {
		if resourceContextInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(resourceDocID); ok {
			resourceID := resourceContextInsertDoc.ResourceID
			resourceIDToDocMap[resourceID] = append(resourceIDToDocMap[resourceID], resourceDocID)

			// Add escape characters if '\' are present
			if strings.Contains(resourceID, `\`) {
				resourceID = common.EscapeString(resourceID)
			}

			resourceIDs = append(resourceIDs, resourceID)
		}
	}

	ignoreTfApproach := ""
	tfApproach := resourceContext.TenantTfApproach

	if tfApproach == "yor" {
		ignoreTfApproach = `,"must_not":[{"match":{"derivedFrom.keyword":"tfAlternate"}}]`
	}

	aggregatedCodeEventsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"userType.keyword":["github","gitlab","bitbucket"]}},{"terms":{"resourceId.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}}] ` + ignoreTfApproach + ` }},"from":0,"size":0,"aggs":{"byResource":{"terms":{"field":"resourceId.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"byUser":{"terms":{"field":"user.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"matchedDoc":{"top_hits":{"size":1,"sort":{"eventTime":{"order":"desc"}},"_source":["userType","derivedFrom","priorityConfigs"]}}}},"byCommitDocId":{"terms":{"field":"commitDocId.keyword","size":10},"aggs":{"latestEventTime":{"top_hits":{"size":1,"sort":[{"eventTime":{"order":"desc"}}]}}}}}}}}`

	codeEventAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.RESOURCE_USER_EVENTS_INDEX}, aggregatedCodeEventsQuery)
	if err != nil {
		return
	}

	if resourceAggregation, ok := codeEventAggregation["byResource"].(map[string]any); ok {
		jsonData, err := json.Marshal(resourceAggregation)
		if err != nil {
			return err
		}

		var resourceAgg struct {
			Buckets []CodeContextBucket `json:"buckets"`
		}
		if err := json.Unmarshal(jsonData, &resourceAgg); err != nil {
			return err
		}

		for _, resourceBucket := range resourceAgg.Buckets {

			for _, resourceDocID := range resourceIDToDocMap[resourceBucket.Key] {

				resourceContextInsertDoc, _ := resourceContext.GetResourceContextInsertDoc(resourceDocID)

				for _, userBucket := range resourceBucket.ByUser.Buckets {
					if err := processUserBucket(&resourceContextInsertDoc, userBucket, resourceContext); err != nil {
						logger.Print(logger.ERROR, "Error processing user bucket", err)
						continue
					}
				}

				if err := processCommitAggregation(resourceBucket, &resourceContextInsertDoc); err != nil {
					logger.Print(logger.ERROR, "Error processing commit aggregation", err)
					continue
				}

				resourceContext.SetResourceContextInsertDoc(resourceDocID, resourceContextInsertDoc)
			}
		}
	}

	return
}

func processUserBucket(resourceContextInsertDoc *common.ResourceContextInsertDoc, userBucket Bucket, resourceContext *rcontext.ResourceContext) error {
	jsonData, err := json.Marshal(userBucket)
	if err != nil {
		return err
	}

	var userBucketStruct Bucket
	if err := json.Unmarshal(jsonData, &userBucketStruct); err != nil {
		return err
	}

	username := userBucketStruct.Key
	if strings.HasSuffix(username, "@users.noreply.github.com") {
		// Github private email usecase
		username = emailutils.ProcessGithubPrivateEmail(username)
	}

	for _, hit := range userBucket.MatchedDoc.Hits.Hits {
		userType := hit.Source.UserType
		deployer := hit.Source.DerivedFrom
		if deployer == common.DERIVEDFROM_DEPLOYMENT_TYPE {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				context.GetUserContextItem(resourceContext, username, common.ConvertToTitleCase(userType), "User has deployed the resource using IaC", "", resourceContextInsertDoc.Account, nil),
			)
		} else {
			resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners,
				context.GetUserContextItem(resourceContext, username, common.ConvertToTitleCase(userType), "User has checked in IaC code in git repository relating to resource", "", resourceContextInsertDoc.Account, nil),
			)
		}

		processPriorityConfigs(&hit.Source, resourceContextInsertDoc)
	}

	return nil
}

func processPriorityConfigs(source *CodeContextSource, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	if len(source.PriorityConfig) > 0 {
		priorityConfigs := make(map[string]any)
		if err := json.Unmarshal([]byte(source.PriorityConfig), &priorityConfigs); err != nil {
			logger.Print(logger.ERROR, "Error Unmarshaling priority configs json", err)
			return
		}

		for _, val := range priorityConfigs {
			if valStr, ok := val.(string); ok {
				processSoftwareNames(valStr, resourceContextInsertDoc)
				processDeploymentNames(valStr, resourceContextInsertDoc)
			}
		}
	}
}

func processSoftwareNames(valStr string, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	var uniqueSoftwares = make(map[string]struct{})

	softwareNames := context.GetSoftwareNameListFromValue(valStr)
	for _, softwareName := range softwareNames {
		if _, exists := uniqueSoftwares[softwareName]; !exists {
			uniqueSoftwares[softwareName] = struct{}{}
			resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
				resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
				common.ResourceContextItem{Name: softwareName, Type: common.IACTEMPLATE_SOFTWARE_TYPE, Desc: context.GetStaticDescriptionOfSoftwareType(common.IACTEMPLATE_SOFTWARE_TYPE)},
			)
		}
	}
}

func processDeploymentNames(valStr string, resourceContextInsertDoc *common.ResourceContextInsertDoc) {
	var uniqueDeployments = make(map[string]struct{})

	deploymentNames := context.GetDeploymentNamesFromValue(valStr)
	for _, deploymentName := range deploymentNames {
		if _, exists := uniqueDeployments[deploymentName]; !exists {
			uniqueDeployments[deploymentName] = struct{}{}
			resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
				common.ResourceContextItem{Name: deploymentName, Type: common.RESOURCE_NAME_DEPLOYMENT_TYPE, Desc: context.GetStaticDescriptionOfDeploymentType(common.RESOURCE_NAME_DEPLOYMENT_TYPE)},
			)
		}
	}
}

func processCommitAggregation(resourceBucket CodeContextBucket, resourceContextInsertDoc *common.ResourceContextInsertDoc) error {

	commitDocAggs := resourceBucket.ByCommitDocId

	commitDocIds := []string{}
	for _, commitDocBucket := range commitDocAggs.Buckets {
		if len(commitDocBucket.Key) > 0 {
			commitDocIds = append(commitDocIds, commitDocBucket.Key)
		}
	}

	if len(commitDocIds) == 0 {
		return nil
	}

	resourceContextInsertDoc.CommitInfo.CommitDocIDs = commitDocIds

	commitQuery := `{"query":{"bool":{"must":[{"terms":{"_id":["` + strings.Join(commitDocIds, `","`) + `"]}}],"must_not":[],"should":[]}},"from":0,"size":0,"sort":[],"aggs":{"file_aggregation":{"terms":{"field":"filename.keyword","size":10},"aggs":{"repo_aggregation":{"terms":{"field":"repoName.keyword","size":10}}}}}}`
	commitAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.IAC_GIT_COMMITS_INDEX}, commitQuery)
	if err != nil {
		return err
	}

	jsonData, err := json.Marshal(commitAggregation["file_aggregation"])
	if err != nil {
		return err
	}

	var fileAgg CommitFileAggregation
	if err := json.Unmarshal(jsonData, &fileAgg); err != nil {
		return err
	}

	for _, commitFileBucket := range fileAgg.Buckets {
		resourceContextInsertDoc.CommitInfo.FileName = commitFileBucket.Key
		for _, repoBucket := range commitFileBucket.RepoAggregation.Buckets {
			resourceContextInsertDoc.CommitInfo.RepoName = repoBucket.Key
			break
		}
	}

	return nil
}
