package context

import (
	"encoding/json"
	"regexp"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	basicutils "github.com/precize/common/basic"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func GetSensitivityNameFromValue(str string) string {

	str = strings.ToLower(str)
	str = strings.TrimSpace(str)

	for sensitivity, pattern := range contextutils.SensitivityKeyOrValues {
		regex := regexp.MustCompile(pattern)
		if regex.MatchString(str) {
			return sensitivity
		}
	}

	return ""
}

func GetSensitivityNameListFromValue(str string) (sensitivityNames []string) {

	str = strings.ToLower(str)
	str = strings.TrimSpace(str)

	for sensitivity, pattern := range contextutils.SensitivityKeyOrValues {
		regex := regexp.MustCompile(pattern)
		if regex.MatchString(str) {
			sensitivityNames = append(sensitivityNames, sensitivity)
		}
	}

	return
}

func GetInheritedSensitivity(parentDoc common.ResourceContextInsertDoc) (inheritedSensitivity []common.ResourceContextItem) {

	if len(parentDoc.DefinedSensitivity) > 0 {
		inheritedSensitivity = append(inheritedSensitivity, parentDoc.DefinedSensitivity...)
	} else if len(parentDoc.InheritedSensitivity) > 0 {
		inheritedSensitivity = append(inheritedSensitivity, parentDoc.InheritedSensitivity...)
	}

	return
}

func GetUniqueSensitivityContext(resourceContextDoc *common.ResourceContextInsertDoc, accountSensitivity *sync.Map) (sensitivity []string) {

	uniqueSensitivity := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedSensitivity, uniqueSensitivity)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedSensitivity, uniqueSensitivity)

	if len(uniqueSensitivity) > 0 {
		switch resourceContextDoc.ResourceType {
		case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
			common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
			common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:

		default:
			var parentContextID string
			var parentSensitivityMap map[string]struct{}

			switch resourceContextDoc.ServiceID {
			case common.AWS_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			case common.AZURE_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_RG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			case common.GCP_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			}

			if parentContextID != "" {
				if existingVal, loaded := accountSensitivity.LoadOrStore(parentContextID, uniqueSensitivity); loaded {
					if existingMap, ok := existingVal.(map[string]struct{}); ok {
						parentSensitivityMap = existingMap
						for k := range uniqueSensitivity {
							parentSensitivityMap[k] = struct{}{}
						}
						accountSensitivity.Store(parentContextID, parentSensitivityMap)
					}
				}
			}
		}
	}

	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedSensitivity, uniqueSensitivity)

	sensitivity = make([]string, 0, len(uniqueSensitivity))
	for sensitivityName := range uniqueSensitivity {
		sensitivity = append(sensitivity, sensitivityName)
	}

	return sensitivity
}

func GetSensitivityContext(entityJSON map[string]any,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, resourceContext *rcontext.ResourceContext) {

	var sensitivityText, sensitivityReqType, fieldName string

	switch resourceContextInsertDoc.ResourceType {
	case common.AWS_DYNAMODB_RESOURCE_TYPE:
		if tableAttrList, ok := entityJSON["tableAttributes"].([]any); ok {
			for _, tableAttr := range tableAttrList {
				if tableAttrStr, ok := tableAttr.(string); ok {
					sensitivityText += tableAttrStr + ","
				}
			}
			sensitivityReqType = DATASET_COLUMN
		}
	case common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:
		type Field struct {
			Mode string `json:"mode,omitempty"`
			Name string `json:"name,omitempty"`
			Type string `json:"type,omitempty"`
		}
		type Schema struct {
			Fields []Field `json:"fields,omitempty"`
		}
		type DatabaseSchema struct {
			Name         string `json:"name,omitempty"`
			Schema       Schema `json:"schema,omitempty"`
			RecordsCount int    `json:"numRows"`
		}

		jsonData, err := json.Marshal(entityJSON)
		if err != nil {
			logger.Print(logger.INFO, "Error marshalling ", []string{resourceContext.TenantID}, err)
			return
		}

		var dbSchema DatabaseSchema
		if err := json.Unmarshal(jsonData, &dbSchema); err != nil {
			logger.Print(logger.INFO, "Error unmarshalling ", []string{resourceContext.TenantID}, err)
			return
		}

		if dbSchema.RecordsCount > 0 {
			for _, field := range dbSchema.Schema.Fields {
				sensitivityText += field.Name + ","
			}
			sensitivityReqType = DATASET_COLUMN
		}
	case common.AWS_ORGANIZATIONPOLICY_RESOURCE_TYPE:
		jsonData, err := json.Marshal(entityJSON)
		if err != nil {
			return
		}

		type PolicyContent struct {
			Ec2Attributes struct {
				VpcBlockPublicAccess struct {
					InternetGatewayBlock struct {
						Mode              map[string]string `json:"mode"`
						ExclusionsAllowed map[string]string `json:"exclusions_allowed"`
					} `json:"internet_gateway_block"`
				} `json:"vpc_block_public_access"`
				ExceptionMessage map[string]string `json:"exception_message"`
			} `json:"ec2_attributes"`
		}

		type PolicyDocument struct {
			Content string `json:"content"`
			Name    string `json:"name"`
			Type    string `json:"type"`
		}

		var policyDoc PolicyDocument
		if err := json.Unmarshal(jsonData, &policyDoc); err != nil {
			return
		}

		var policyContent PolicyContent
		if err := json.Unmarshal([]byte(policyDoc.Content), &policyContent); err != nil {
			return
		}

		if exceptionMsg, ok := policyContent.Ec2Attributes.ExceptionMessage["@@assign"]; ok {
			sensitivityReqType = RAW_DATA
			sensitivityText = exceptionMsg
			fieldName += "exception_message"
		}

	case common.AWS_EC2_RESOURCE_TYPE, common.AZURE_VM_RESOURCE_TYPE:

		if encodedStartupScript, ok := entityJSON["userData"].(string); ok {
			decodedStartupScript := basicutils.DecodeBase64Recursive(encodedStartupScript)

			if len(decodedStartupScript) > 0 {
				sensitivityResp := false

				if strings.Contains(strings.ToLower(decodedStartupScript), "openssh private key") || strings.Contains(strings.ToLower(decodedStartupScript), "rsa private key") {
					sensitivityResp = true
				} else {
					sensitivityResp = common.DeriveSensitivityFromInstanceStartupScript(decodedStartupScript, resourceContext.TenantID)
				}

				if sensitivityResp {
					resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
						common.ResourceContextItem{
							Name: contextutils.PII_SENSITIVITY,
							Type: common.RESOURCE_DATA_SENSITIVITY_TYPE,
							Desc: GetStaticDescriptionOfSensitivityType(common.RESOURCE_DATA_SENSITIVITY_TYPE) + " (Property: UserData)",
						},
					)
				}

				return
			}
		}
	case common.GCP_INSTANCE_RESOURCE_TYPE:

		isDataSensitive := func(propValue string) {
			if scriptData, ok := entityJSON[propValue].(string); ok {

				dataSensitive := common.DeriveSensitivityFromInstanceStartupScript(scriptData, resourceContext.TenantID)
				if dataSensitive {
					resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
						common.ResourceContextItem{
							Name: contextutils.PII_SENSITIVITY,
							Type: common.RESOURCE_DATA_SENSITIVITY_TYPE,
							Desc: GetStaticDescriptionOfSensitivityType(common.RESOURCE_DATA_SENSITIVITY_TYPE) + " (Property: " + common.ConvertToTitleCase(common.SeparateCamelCaseWithSpecialChar(propValue, " ")) + ")",
						},
					)

				}
			}
		}

		isDataSensitive("startupScript")
		isDataSensitive("shutdownScript")
		return
	}

	if len(sensitivityText) > 0 {
		DetectSensitivity(sensitivityText, resourceContextInsertDoc.TenantID, sensitivityReqType, fieldName, resourceContextInsertDoc, resourceContext)
	}
}

type DetectionResult struct {
	Type         string
	MatchedTerms []string
}

type SensitivityDataDetector struct {
	patterns map[string][]*regexp.Regexp
}

func NewSensitivityDataDetector() *SensitivityDataDetector {
	return &SensitivityDataDetector{
		patterns: map[string][]*regexp.Regexp{
			contextutils.PII_SENSITIVITY: {
				regexp.MustCompile(`\b\d{3}-\d{2}-\d{4}\b`),                                          // Social Security Number
				regexp.MustCompile(`\b[A-Z]{1}\d{6,8}\b`),                                            // Passport Number
				regexp.MustCompile(`\b\d{2}-\d{7}\b`),                                                // Driver's License Number
				regexp.MustCompile(`\b[A-Za-z]+\s[A-Za-z]+,\s[A-Za-z]+\b`),                           // Full Name Pattern
				regexp.MustCompile(`\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b`),            // Email with more comprehensive matching
				regexp.MustCompile(`\b\d{10}\b`),                                                     // Phone Number (10 digits)
				regexp.MustCompile(`\b\d{3}-\d{3}-\d{4}\b`),                                          // Formatted Phone Number
				regexp.MustCompile(`\b(Date\s?of\s?Birth|DOB):\s?\d{4}-\d{2}-\d{2}\b`),               // Date of Birth
				regexp.MustCompile(`\b(Password\s?Number|Password\s?ID):\s?[A-Za-z0-9]+\b`),          // Password Number
				regexp.MustCompile(`\b(Birth\s?Date):\s?\d{4}-\d{2}-\d{2}\b`),                        // Alternative Birth Date Format
				regexp.MustCompile(`\bDOB\s?=\s?\d{4}-\d{2}-\d{2}\b`),                                // DOB with equals sign
				regexp.MustCompile(`(?i)\b(First\s?Name|Last\s?Name|Age|Location|Place|Passport)\b`), // Common PII Keywords
			},
			contextutils.PHI_SENSITIVITY: {
				regexp.MustCompile(`\b[A-Z]{2,3}\d{6,8}\b`),                              // Medical Record Number
				regexp.MustCompile(`\b\d{5,10}-[A-Za-z]{1,3}\b`),                         // Patient ID
				regexp.MustCompile(`\b\d{9}\b`),                                          // Health Plan Identifier (HPID)
				regexp.MustCompile(`(?i)\b(Condition|Diagnosis|Treatment)[A-Za-z\s]+\b`), // Medical Condition Descriptors
				regexp.MustCompile(`(?i)\b(Medication|Prescription)[A-Za-z\s]+\b`),       // Medication Information
				regexp.MustCompile(`(?i)\b(Medical\s?Record|Patient\s?Information)\b`),   // Medical Record Keywords
				regexp.MustCompile(`(?i)\b(Health\s?Condition|Medical\s?History)\b`),     // Additional Medical Keywords
			},
			contextutils.PCI_SENSITIVITY: {
				regexp.MustCompile(`\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b`),           // Generic Credit Card
				regexp.MustCompile(`\b3[47]\d{2}[-\s]?\d{6}[-\s]?\d{5}\b`),                 // American Express
				regexp.MustCompile(`\b4\d{3}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b`),          // Visa
				regexp.MustCompile(`\b5[1-5]\d{2}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b`),     // MasterCard
				regexp.MustCompile(`\b6(?:011|5\d{2})[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b`), // Discover
				regexp.MustCompile(`\b\d{3,4}\b`),                                          // CVV
				regexp.MustCompile(`Expiration:\s\d{2}/\d{2}\b`),                           // Expiration Date
				regexp.MustCompile(`(?i)\b(Credit\s?Card|Debit\s?Card)\b`),                 // Card Type Keywords
				regexp.MustCompile(`(?i)\b(Payment\s?Card|Card\s?Number)\b`),               // Payment Card Keywords
				regexp.MustCompile(`(?i)\b(Card\s?Details|Card\s?Information)\b`),          // Card Information Keywords
			},
			contextutils.INTERNAL_SENSITIVITY: {
				regexp.MustCompile(`(?i)\b(Internal|Confidential)\s(Document|Report|Memo)\b`), // Internal Document Markers
				regexp.MustCompile(`\b[A-Z]{2,3}-\d{4}-[A-Z]{2,3}\b`),                         // Internal Project Codes
				regexp.MustCompile(`(?i)\bConfidential:\s[A-Za-z\s]+\b`),                      // Confidential Markers
				regexp.MustCompile(`(?i)\b(Internal\s?Use|For\s?Internal\s?Use)\b`),           // Internal Use Keywords
				regexp.MustCompile(`(?i)\b(Company\s?Sensitive|Proprietary\s?Information)\b`), // Internal Sensitivity Keywords
			},
			contextutils.CONFIDENTIAL_SENSITIVITY: {
				regexp.MustCompile(`(?i)\b(Confidential)\s(Information|Data)\b`),           // Confidential Information Markers
				regexp.MustCompile(`(?i)\b(Strictly\s)?Confidential\b`),                    // Strict Confidentiality Markers
				regexp.MustCompile(`(?i)\bNDA\s(Covered|Protected)\b`),                     // Non-Disclosure Agreement Indicators
				regexp.MustCompile(`(?i)\b(Proprietary|Trade\sSecret):\s[A-Za-z\s]+\b`),    // Proprietary Information
				regexp.MustCompile(`(?i)\b(Confidential\s?Document|Sensitive\s?Data)\b`),   // Confidential Document Keywords
				regexp.MustCompile(`(?i)\b(Restricted\s?Access|Limited\s?Distribution)\b`), // Access Restriction Keywords
			},
			contextutils.RESTRICTED_SENSITIVITY: {
				regexp.MustCompile(`(?i)\b(Restricted)\s(Access|Distribution)\b`),          // Restricted Access Markers
				regexp.MustCompile(`(?i)\b(Top\s)?Secret\b`),                               // Security Classification
				regexp.MustCompile(`(?i)\bAccess\sLevel:\s(Top\sSecret|Restricted)\b`),     // Access Level Indicators
				regexp.MustCompile(`(?i)\b(Need-to-Know|Compartmented)\s(Information)\b`),  // Restricted Information Markers
				regexp.MustCompile(`(?i)\b(Highly\s?Sensitive|Extreme\s?Sensitivity)\b`),   // Sensitivity Level Keywords
				regexp.MustCompile(`(?i)\b(Classified\s?Information|Restricted\s?Data)\b`), // Restricted Information Keywords
			},
			contextutils.PUBLIC_SENSITIVITY: {
				regexp.MustCompile(`(?i)\b(Public)\s(Document|Information)\b`),         // Public Information Markers
				regexp.MustCompile(`(?i)\bRelease\sDate:\s\d{4}-\d{2}-\d{2}\b`),        // Public Release Dates
				regexp.MustCompile(`(?i)\b(Press\sRelease|Public\sNotice)\b`),          // Public Communication Markers
				regexp.MustCompile(`(?i)\bPublicly\sAvailable\b`),                      // Public Availability Indicators
				regexp.MustCompile(`(?i)\b(Open\s?Access|General\s?Information)\b`),    // Public Access Keywords
				regexp.MustCompile(`(?i)\b(Shared\s?Publicly|Available\s?to\s?All)\b`), // Public Sharing Keywords
			},
		},
	}
}

func DetectSensitivity(text, tenantID, reqType, fieldName string, resourceContextInsertDoc *common.ResourceContextInsertDoc, resourceContext *rcontext.ResourceContext) []DetectionResult {
	d := NewSensitivityDataDetector()
	var results []DetectionResult

	for dataType, regexes := range d.patterns {
		var matchedTerms []string
		for _, regex := range regexes {
			matches := regex.FindAllString(text, -1)
			matchedTerms = append(matchedTerms, matches...)
		}

		if len(matchedTerms) > 0 {
			results = append(results, DetectionResult{
				Type:         dataType,
				MatchedTerms: matchedTerms,
			})

			rctxType := common.RESOURCE_DATA_SENSITIVITY_TYPE
			if len(fieldName) > 0 {
				fieldName = ", Fields: " + fieldName
				rctxType += fieldName
			}

			resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
				common.ResourceContextItem{
					Name: dataType,
					Type: common.RESOURCE_DATA_SENSITIVITY_TYPE,
					Desc: GetStaticDescriptionOfSensitivityType(common.RESOURCE_DATA_SENSITIVITY_TYPE) + " (" + fieldName + ": " + strings.Join(matchedTerms, ", ") + ")",
				},
			)
		}
	}

	if len(results) == 0 {
		sensitivityList := getSensitivityFromTextOpenAI(text, tenantID, reqType, resourceContext)
		for sensitivity, matchedText := range sensitivityList {
			resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
				common.ResourceContextItem{
					Name: sensitivity,
					Type: common.RESOURCE_DATA_SENSITIVITY_TYPE,
					Desc: GetStaticDescriptionOfSensitivityType(common.RESOURCE_DATA_SENSITIVITY_TYPE) + " (" + strings.Join(matchedText, ", ") + ")",
				},
			)
		}
	}
	return results
}

func getSensitivityFromTextOpenAI(desc, tenantID, requestType string, resourceContext *rcontext.ResourceContext) map[string][]string {
	var openAIMessages []common.OpenAIMessage
	sensitivityColumnsMap := make(map[string][]string)

	switch requestType {
	case RAW_DATA:
		openAIMessages = []common.OpenAIMessage{
			{
				Role:    "system",
				Content: "You are an expert in data sensitivity classification.",
			},
			{
				Role: "user",
				Content: `Analyze the text for sensitivity levels. Be strict and classify only text that clearly matches these categories. 
					Possible sensitivity categories:
					- PII 
					- PCI
 					- PHI
					Provide the output in the following strictly in the below format if present otherwise return "":
					[{
						"Sensitivity": "sensitivity category",
						"Columns Matched": "texts that match the sensitivity category (max 3)"
					}]
					Text to analyze: ` + desc,
			},
		}
	case DATASET_COLUMN:
		openAIMessages = []common.OpenAIMessage{
			{
				Role:    "system",
				Content: "You are an expert in data sensitivity classification.",
			},
			{
				Role: "user",
				Content: `Evaluate the dataset columns for sensitivity levels. Be strict and classify only text that clearly matches these categories. 
					Dataset columns: ` + desc + `
					Possible sensitivity categories:
					- PII 
					- PCI
					- PHI
					Provide the output in the following strictly in the below format if present otherwise return "":
					[{
						"Sensitivity": "sensitivity category",
						"Columns Matched": "columns that match the sensitivity category (max 3 columns)"
					}]`,
			},
		}
	}
	sensitivityStr := common.DeriveSensitivityFromText(desc, tenantID, openAIMessages)

	if sensitivityStr == "" {
		return sensitivityColumnsMap
	}

	var inputDataList []map[string]string
	if err := json.Unmarshal([]byte(sensitivityStr), &inputDataList); err != nil {
		return sensitivityColumnsMap
	}

	for _, entry := range inputDataList {
		sensitivityStr, ok := entry["Sensitivity"]
		if !ok {
			continue
		}

		columnsStr, ok := entry["Columns Matched"]
		if !ok {
			continue
		}

		columns := strings.Split(columnsStr, ",")
		for i, col := range columns {
			columns[i] = strings.TrimSpace(col)
		}

		var updatedCols []string
		for _, column := range columns {
			if exemptedCols, ok := resourceContext.GetSensitivityExceptions(sensitivityStr); ok {
				if !slices.Contains(exemptedCols, column) {
					updatedCols = append(updatedCols, column)
				}
			}
		}

		if len(updatedCols) > 0 {
			sensitivityColumnsMap[sensitivityStr] = updatedCols
		}
	}

	return sensitivityColumnsMap
}

func GetSensitivityExceptionsForTenant() map[string][]string {
	exceptions := make(map[string][]string)

	sensitityExemptionsQuery := `{"query":{"bool":{"filter":[{"match":{"type.keyword":"data_sensitivity"}},{"match":{"op.keyword":"ne"}}]}}}`
	sensitityExemptionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, sensitityExemptionsQuery)
	if err != nil {
		return exceptions
	}

	for _, sensitityExemptionsDoc := range sensitityExemptionsDocs {
		key, _ := sensitityExemptionsDoc["key"].(string)
		values, _ := sensitityExemptionsDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				exceptions[key] = append(exceptions[key], val)
			}
		}
	}

	return exceptions
}

func GetStaticDescriptionOfSensitivityType(sensitivityType string) (desc string) {

	switch sensitivityType {
	case common.RESOURCE_NAME_SENSITIVITY_TYPE:
		desc = "Sensitivity has been derived from the resource name"
	case common.CUSTOMER_DEFINED_SENSITIVITY_TYPE:
		desc = "Sensitivity has been assigned from Precize console"
	case common.PRECIZE_DEFINED_SENSITIVITY_TYPE:
		desc = "Sensitivity has been assigned by Precize"
	case common.RESOURCE_DATA_SENSITIVITY_TYPE:
		desc = "Sensitivity has been derived from resource properties"
	case common.ORCACONTEXT_SENSITIVITY_TYPE:
		desc = "Sensitivity has been derived from Orca findings"
	case common.JIRACONTEXT_SENSITIVITY_TYPE:
		desc = "Sensitivity has been derived by extracting a corresponding Jira issue"
	case common.DESC_SENSITIVITY_TYPE:
		desc = "Sensitivity has been derived from the resource description"
	case common.CHILD_SENSITIVITY_TYPE:
		desc = "Sentivity derived from a child resource"
	case common.INHERITED_SENSITIVITY_TYPE:
		desc = "Sensitivity inherited from a parent resource"
	}

	if len(desc) == 0 && strings.Contains(sensitivityType, contextutils.TAG_PREFIX) {
		desc = "Sensitivity has been derived from tag"
	}

	return
}

func EnhanceContextItemForSensitivity(resourceContextDoc *common.ResourceContextInsertDoc, sensitivity []string) {

	for i, definedSensitivity := range resourceContextDoc.ResourceSensitivityTypes.DefinedSensitivity {
		if slices.Contains(sensitivity, definedSensitivity.Name) {
			definedSensitivity.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedSensitivity.Desc) == 0 {
			definedSensitivity.Desc = GetStaticDescriptionOfSensitivityType(definedSensitivity.Type)
		}

		resourceContextDoc.ResourceSensitivityTypes.DefinedSensitivity[i] = definedSensitivity
	}

	for i, derivedSensitivity := range resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity {
		if slices.Contains(sensitivity, derivedSensitivity.Name) {
			derivedSensitivity.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedSensitivity.Desc) == 0 {
			derivedSensitivity.Desc = GetStaticDescriptionOfSensitivityType(derivedSensitivity.Type)
		}

		resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity[i] = derivedSensitivity
	}

	for i, inheritedSensitivity := range resourceContextDoc.ResourceSensitivityTypes.InheritedSensitivity {

		if slices.Contains(sensitivity, inheritedSensitivity.Name) {
			inheritedSensitivity.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		inheritedSensitivity.Type = common.INHERITED_SENSITIVITY_TYPE
		inheritedSensitivity.Desc = GetStaticDescriptionOfSensitivityType(inheritedSensitivity.Type)

		resourceContextDoc.ResourceSensitivityTypes.InheritedSensitivity[i] = inheritedSensitivity
	}
}
