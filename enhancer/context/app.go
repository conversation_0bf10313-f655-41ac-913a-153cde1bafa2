package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func GetAppNameFromValue(str string, opts ...rcontext.ContextCriteriaOptions) (app string) {

	var (
		matched    string
		matchedLen int
		criteria   rcontext.ContextCriteria
	)

	for _, opt := range opts {
		opt(&criteria)
	}

	str = strings.ToLower(str)

	for appName, values := range contextutils.AppValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) && len(appName) > matchedLen {
				if !unsupportedApp(appName, criteria) {
					matchedLen = len(appName)
					matched = appName
				}
			}
		}
	}

	if len(matched) > 0 {
		app = matched
	}

	return
}

func GetAppNameListFromValue(str string, opts ...rcontext.ContextCriteriaOptions) []string {

	var (
		appNames = make([]string, 0)
		criteria rcontext.ContextCriteria
	)

	str = strings.ToLower(str)

	for _, opt := range opts {
		opt(&criteria)
	}

	for app, values := range contextutils.AppValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				if !unsupportedApp(app, criteria) {
					appNames = append(appNames, app)
				}
			}
		}
	}

	return appNames
}

func GetUniqueAppContext(resourceContextDoc *common.ResourceContextInsertDoc) (app []string) {

	uniqueApp := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedApp, uniqueApp)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedApp, uniqueApp)

	for appName := range uniqueApp {
		app = append(app, appName)
	}

	return
}

func AddApplicationToGlobalApps(application, tenantID string) {

	contextutils.GlobalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultAppValue := range contextutils.DefaultAppValues {
		subApps := strings.Split(defaultAppValue, ",")
		for _, subApp := range subApps {
			if strings.ToLower(subApp) == strings.ToLower(application) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		// before adding the app into DefaultAppValues check if it is an actual app
		if common.VerifyAppName(application, tenantID, contextutils.AIRejectedAppValues) {
			contextutils.DefaultAppValues = append(contextutils.DefaultAppValues, application)
		}
	}

	contextutils.GlobalValuesMutex.Unlock()
}

func unsupportedApp(appName string, criteria rcontext.ContextCriteria) bool {

	switch appName {
	case contextutils.JENKINS_APP, contextutils.ARGO_APP, contextutils.CICD_APP, contextutils.GITHUB_APP, contextutils.GITLAB_APP, contextutils.BITBUCKET_APP:
		switch criteria.ResourceType {
		case common.AWS_EC2_RESOURCE_TYPE, common.GCP_INSTANCE_RESOURCE_TYPE, common.AZURE_VM_RESOURCE_TYPE:
		default:
			return true
		}
	case contextutils.TERRAFORM_APP:
		switch criteria.ResourceType {
		case common.AZURE_GRAPHAPP_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE:
		default:
			return true
		}
	}

	return false
}

func ProcessResourceNamesForApps(resourceContext *rcontext.ResourceContext, contextDocIDs []string) {

	var resourceNames = make([]string, 0, len(contextDocIDs))

	for _, contextDocID := range contextDocIDs {
		if contextDoc, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {

			// exclude few resource types

			switch contextDoc.ResourceType {
			case common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.AZURE_GRAPHAPP_RESOURCE_TYPE, common.GCP_ROLE_RESOURCE_TYPE, common.AWS_IAMPOLICY_RESOURCE_TYPE, common.IPDETAILS_RESOURCE_TYPE:
				continue
			}

			if len(contextDoc.ResourceName) > 0 {
				resourceNames = append(resourceNames, strings.ToLower(contextDoc.ResourceName))
			}
		}
	}

	apps := common.GetAppsFromRscNames(resourceNames, resourceContext.TenantID)

	for app, rscName := range apps {

		excludeApp := false

		for _, defaultAppValue := range contextutils.DefaultAppValues {
			if strings.ToLower(defaultAppValue) == strings.ToLower(app) {
				excludeApp = true
				break
			}
		}

		if excludeApp {
			continue
		}

		if _, ok := contextutils.AppValues[app]; ok || ignoreGlobalAppAddition(app) {
			continue
		}

		app = resourceutils.FormatContextValue(app)
		AddApplicationToAIDetectedGlobalApps(app, rscName)
	}
}

func AddApplicationToAIDetectedGlobalApps(application, rscName string) {

	contextutils.GlobalValuesMutex.Lock()

	if _, ok := contextutils.AIDetectedApps[application]; !ok {
		contextutils.AIDetectedApps[application] = rscName
	}

	contextutils.GlobalValuesMutex.Unlock()
}

// should global app detected by ai be ignored
func ignoreGlobalAppAddition(app string) bool {
	text := "ignoreGlobalApp:::" + strings.ToLower(app)
	textLookupDocID := common.GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		if ignoreApp, ok := doc["hasName"].(bool); ok {
			return ignoreApp
		}
	}

	return false
}

func GetStaticDescriptionOfAppType(applicationType string) (desc string) {

	switch applicationType {
	case common.RESOURCE_NAME_APP_TYPE:
		desc = "Application has been derived from the resource name"
	case common.IACTEMPLATE_APP_TYPE:
		desc = "Application has been derived from IaC template"
	case common.ORCACONTEXT_APP_TYPE:
		desc = "Application has been derived from Orca findings"
	case common.CUSTOMER_DEFINED_APP_TYPE:
		desc = "Application has been assigned from Precize console"
	case common.PRECIZE_DEFINED_APP_TYPE:
		desc = "Application has been assigned by Precize"
	case common.DEFENDERCONTEXT_APP_TYPE:
		desc = "Application has been derived from Defender findings"
	case common.JIRA_APP_TYPE:
		desc = "Application has been derived by extracting a corresponding Jira issue"
	case common.RELATED_RESOURCE_APP_TYPE:
		desc = "Application has been derived from a related resource"
	case common.DESC_APP_TYPE:
		desc = "Application has been derived from the resource description"
	case common.SIMILAR_RESOURCENAME_APP_TYPE:
		desc = "Application has been derived from a similarly named resource"
	case common.SAME_TAG_APP_TYPE:
		desc = "Application has been derived from a similarly tagged resource"
	case common.ACTIVITY_APP_TYPE:
		desc = "Application has been derived from activities"
	}

	if len(desc) == 0 && strings.Contains(applicationType, contextutils.TAG_PREFIX) {
		desc = "Environment has been derived from tag"
	}

	return
}

func EnhanceContextItemForApp(resourceContextDoc *common.ResourceContextInsertDoc, app []string) {

	for i, definedApp := range resourceContextDoc.ResourceAppTypes.DefinedApp {
		if slices.Contains(app, definedApp.Name) {
			definedApp.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedApp.Desc) == 0 {
			definedApp.Desc = GetStaticDescriptionOfAppType(definedApp.Type)
		}

		resourceContextDoc.ResourceAppTypes.DefinedApp[i] = definedApp
	}

	for i, derivedApp := range resourceContextDoc.ResourceAppTypes.DerivedApp {
		if slices.Contains(app, derivedApp.Name) {
			derivedApp.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedApp.Desc) == 0 {
			derivedApp.Desc = GetStaticDescriptionOfAppType(derivedApp.Type)
		}

		resourceContextDoc.ResourceAppTypes.DerivedApp[i] = derivedApp
	}
}
