package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func IsTeamKey(tagKey string) bool {

	for r := range contextutils.TeamTagKeys {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetTeamNameFromValue(str string) (team string) {

	var (
		matched    string
		matchedLen int
	)

	str = strings.ToLower(str)

	for teamName, values := range contextutils.TeamValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {
				if len(teamName) > matchedLen {
					matchedLen = len(teamName)
					matched = teamName
				}
			}
		}
	}

	if len(matched) > 0 {
		team = matched
	}

	return
}

func GetTeamNameListFromValue(str string) (teamNames []string) {

	str = strings.ToLower(str)

	for teamName, values := range contextutils.TeamValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {
				teamNames = append(teamNames, teamName)
			}
		}
	}

	return
}

func GetInheritedTeam(parentDoc common.ResourceContextInsertDoc) (inheritedTeam []common.ResourceContextItem) {

	if len(parentDoc.DefinedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.DefinedTeam...)
	} else if len(parentDoc.DerivedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.DerivedTeam...)
	} else if len(parentDoc.InheritedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.InheritedTeam...)
	}

	return
}

func GetUniqueTeamContext(resourceContextDoc *common.ResourceContextInsertDoc) (team []string) {

	uniqueTeam := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedTeam, uniqueTeam)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedTeam, uniqueTeam)
	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedTeam, uniqueTeam)

	for teamName := range uniqueTeam {
		team = append(team, teamName)
	}

	return
}

func AddTeamToGlobalTeams(team, tenantID string) {

	contextutils.GlobalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultTeamValue := range contextutils.DefaultTeamValues {
		subTeams := strings.Split(defaultTeamValue, ",")
		for _, subTeam := range subTeams {
			if strings.ToLower(subTeam) == strings.ToLower(team) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		// before adding the team into defaultTeamValues check if it is an actual team
		if common.VerifyTeamName(team, tenantID, contextutils.AIRejectedTeamValues) {
			contextutils.DefaultTeamValues = append(contextutils.DefaultTeamValues, team)
		}
	}

	contextutils.GlobalValuesMutex.Unlock()
}

func ProcessResourceNamesForTeams(resourceContext *rcontext.ResourceContext, contextDocIDs []string) {

	var resourceNames = make([]string, 0, len(contextDocIDs))

	for _, contextDocID := range contextDocIDs {
		if contextDoc, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
			if len(contextDoc.ResourceName) > 0 {
				resourceNames = append(resourceNames, strings.ToLower(contextDoc.ResourceName))
			}
		}
	}

	teams := common.TeamNamesFromList(resourceNames, resourceContext.TenantID)

	for _, team := range teams {
		team = resourceutils.FormatContextValue(team)
		AddTeamToGlobalTeams(team, resourceContext.TenantID)
	}
}

func GetStaticDescriptionOfTeamType(teamType string) (desc string) {

	switch teamType {
	case common.ORGUNIT_NAME_TEAM_TYPE, common.ACCOUNT_NAME_TEAM_TYPE, common.MGMTGRP_NAME_TEAM_TYPE, common.SUBSCRIPTION_NAME_TEAM_TYPE, common.RG_NAME_TEAM_TYPE, common.FOLDER_NAME_TEAM_TYPE, common.PROJECT_NAME_TEAM_TYPE, common.RESOURCE_NAME_TEAM_TYPE:
		desc = "Team has been derived from the resource name"
	case common.RESOURCE_OWNER_TEAM_TYPE:
		desc = "Team has been derived from resource owner "
	case common.CUSTOMER_DEFINED_TEAM_TYPE:
		desc = "Team has been assigned from Precize console"
	case common.RELATED_RESOURCE_TEAM_TYPE:
		desc = "Team has been derived from a related resource"
	case common.PRECIZE_DEFINED_TEAM_TYPE:
		desc = "Team has been assigned by Precize"
	case common.DESC_TEAM_TYPE:
		desc = "Team has been derived from the resource description"
	case common.SIMILAR_RESOURCENAME_TEAM_TYPE:
		desc = "Team has been derived from a similarly named resource"
	case common.SAME_APP_TEAM_TYPE:
		desc = "Team has been derived from a resource which is part of the same app"
	case common.SAME_TAG_TEAM_TYPE:
		desc = "Team has been derived from a similarly tagged resource"
	case common.INHERITED_TEAM_TYPE:
		desc = "Team inherited from a parent resource"
	}

	if len(desc) == 0 && strings.Contains(teamType, contextutils.TAG_PREFIX) {
		desc = "Team has been derived from tag"
	}

	return
}

func EnhanceContextItemForTeam(resourceContextDoc *common.ResourceContextInsertDoc, team []string) {

	for i, definedTeam := range resourceContextDoc.ResourceTeamTypes.DefinedTeam {
		if slices.Contains(team, definedTeam.Name) {
			definedTeam.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedTeam.Desc) == 0 {
			definedTeam.Desc = GetStaticDescriptionOfTeamType(definedTeam.Type)
		}

		resourceContextDoc.ResourceTeamTypes.DefinedTeam[i] = definedTeam
	}

	for i, derivedTeam := range resourceContextDoc.ResourceTeamTypes.DerivedTeam {
		if slices.Contains(team, derivedTeam.Name) {
			derivedTeam.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedTeam.Desc) == 0 {
			derivedTeam.Desc = GetStaticDescriptionOfTeamType(derivedTeam.Type)
		}

		resourceContextDoc.ResourceTeamTypes.DerivedTeam[i] = derivedTeam
	}

	for i, inheritedTeam := range resourceContextDoc.ResourceTeamTypes.InheritedTeam {
		if slices.Contains(team, inheritedTeam.Name) {
			inheritedTeam.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		inheritedTeam.Type = common.INHERITED_TEAM_TYPE
		inheritedTeam.Desc = GetStaticDescriptionOfTeamType(inheritedTeam.Type)

		resourceContextDoc.ResourceTeamTypes.InheritedTeam[i] = inheritedTeam
	}
}
