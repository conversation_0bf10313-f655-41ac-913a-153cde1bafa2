package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func GetEnvironmentNameFromValue(str string) string {

	str = strings.ToLower(str)

	for env, values := range contextutils.EnvValues {

		for _, val := range values {

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {

				isEnv := true

				for _, notVal := range contextutils.EnvNotValues[val] {
					regex2 := regexp.MustCompile(notVal)
					if regex2.MatchString(str) {
						isEnv = false
					}
				}

				if isEnv {
					return env
				}
			}
		}
	}

	return ""
}

func GetInheritedEnv(parentDoc common.ResourceContextInsertDoc) (inheritedEnv []common.ResourceContextItem) {

	if len(parentDoc.DefinedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.DefinedEnv...)
	} else if len(parentDoc.DerivedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.DerivedEnv...)
	} else if len(parentDoc.InheritedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.InheritedEnv...)
	}

	return
}

func IncrementParentChildEnvCount(resourceContext *rcontext.ResourceContext, env, accountID, resourceGroup string) {
	if len(accountID) > 0 {
		value, exists := resourceContext.ParentChildEnv.Load(accountID)
		var envCounts map[string]int
		if exists {
			envCounts = value.(map[string]int)
		} else {
			envCounts = make(map[string]int)
		}

		envCounts[env]++
		resourceContext.ParentChildEnv.Store(accountID, envCounts)
	}

	if len(resourceGroup) > 0 {
		value, exists := resourceContext.ParentChildEnv.Load(resourceGroup)
		var envCounts map[string]int
		if exists {
			envCounts = value.(map[string]int)
		} else {
			envCounts = make(map[string]int)
		}

		envCounts[env]++
		resourceContext.ParentChildEnv.Store(resourceGroup, envCounts)
	}
}

func getMaxChildEnvOfParent(parentChildEnv map[string]int) (env string) {
	var max = 20

	for envKey, count := range parentChildEnv {
		if count > max {
			max = count
			env = envKey
		} else if count == max {
			// No clear majority
			env = ""
		}
	}
	return
}

func GetUniqueEnvContext(resourceContextDoc *common.ResourceContextInsertDoc, r *rcontext.ResourceContext) (env []string) {

	uniqueEnv := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedEnv, uniqueEnv)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedEnv, uniqueEnv)
	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedEnv, uniqueEnv)

	for envName := range uniqueEnv {
		env = append(env, envName)
	}

	if len(env) <= 0 {

		// Parent env from child env

		switch resourceContextDoc.ResourceType {
		case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
			common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
			common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:

			if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.ResourceID); exists {
				maxChildEnv := getMaxChildEnvOfParent(childEnvs)
				if len(maxChildEnv) > 0 {
					resourceContextDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: maxChildEnv,
							Type: common.MAJORITY_ENV_TYPE,
							Desc: GetStaticDescriptionOfEnvType(common.MAJORITY_ENV_TYPE),
						},
					)
					env = []string{maxChildEnv}
				}
			}
		}
	}

	if len(resourceContextDoc.ResourceEnvTypes.InheritedEnv) <= 0 {

		// Child env from parent env derived from child

		switch resourceContextDoc.ResourceType {
		case common.AZURE_TENANT_RESOURCE_TYPE, common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE:
			// no parent env
		default:

			var maxChildEnv string

			if len(resourceContextDoc.ResourceGroup) > 0 {
				if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.ResourceGroup); exists {
					maxChildEnv = getMaxChildEnvOfParent(childEnvs)
				}
			}

			if len(maxChildEnv) <= 0 && len(resourceContextDoc.Account) > 0 {
				if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.Account); exists {
					maxChildEnv = getMaxChildEnvOfParent(childEnvs)
				}
			}

			if len(maxChildEnv) > 0 {
				resourceContextDoc.ResourceEnvTypes.InheritedEnv = append(resourceContextDoc.ResourceEnvTypes.InheritedEnv,
					common.ResourceContextItem{
						Name: maxChildEnv,
						Type: common.MAJORITY_ENV_TYPE,
						Desc: GetStaticDescriptionOfEnvType(common.MAJORITY_ENV_TYPE),
					},
				)

				if !slices.Contains(env, maxChildEnv) {
					env = append(env, maxChildEnv)
				}
			}
		}
	}

	return
}

func GetStaticDescriptionOfEnvType(envType string) (desc string) {

	switch envType {
	case common.ACCOUNT_NAME_ENV_TYPE, common.RG_NAME_ENV_TYPE, common.SUBSCRIPTION_NAME_ENV_TYPE, common.ORG_NAME_ENV_TYPE, common.FOLDER_NAME_ENV_TYPE, common.PROJECT_NAME_ENV_TYPE, common.RESOURCE_NAME_ENV_TYPE, common.OPENAIPROJECT_NAME_ENV_TYPE, common.TENANT_NAME_ENV_TYPE, common.MGMTGRP_NAME_ENV_TYPE, common.ORGUNIT_NAME_ENV_TYPE:
		desc = "Environment has been derived from the resource name"
	case common.CUSTOMER_DEFINED_ENV_TYPE:
		desc = "Environment has been assigned from Precize console"
	case common.PRECIZE_DEFINED_ENV_TYPE:
		desc = "Environment has been assigned by Precize"
	case common.MAJORITY_ENV_TYPE:
		desc = "Environment has been derived by way of majority"
	case common.JIRA_ENV_TYPE:
		desc = "Environment has been derived by extracting a corresponding Jira issue"
	case common.INHERITED_ENV_TYPE:
		desc = "Environment has been derived from parent resource"
	}

	if len(desc) == 0 && strings.Contains(envType, contextutils.TAG_PREFIX) {
		desc = "Environment has been derived from tag"
	}

	return
}

func EnhanceContextItemForEnv(resourceContextDoc *common.ResourceContextInsertDoc, env []string) {

	for i, definedEnv := range resourceContextDoc.ResourceEnvTypes.DefinedEnv {
		if slices.Contains(env, definedEnv.Name) {
			definedEnv.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedEnv.Desc) == 0 {
			definedEnv.Desc = GetStaticDescriptionOfEnvType(definedEnv.Type)
		}

		resourceContextDoc.ResourceEnvTypes.DefinedEnv[i] = definedEnv
	}

	for i, derivedEnv := range resourceContextDoc.ResourceEnvTypes.DerivedEnv {
		if slices.Contains(env, derivedEnv.Name) {
			derivedEnv.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedEnv.Desc) == 0 {
			derivedEnv.Desc = GetStaticDescriptionOfEnvType(derivedEnv.Type)
		}

		resourceContextDoc.ResourceEnvTypes.DerivedEnv[i] = derivedEnv
	}

	for i, inheritedEnv := range resourceContextDoc.ResourceEnvTypes.InheritedEnv {
		if slices.Contains(env, inheritedEnv.Name) {
			inheritedEnv.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		inheritedEnv.Type = common.INHERITED_ENV_TYPE
		inheritedEnv.Desc = GetStaticDescriptionOfEnvType(inheritedEnv.Type)

		resourceContextDoc.ResourceEnvTypes.InheritedEnv[i] = inheritedEnv
	}
}
