package related

import (
	"encoding/json"

	"github.com/precize/common"
	"github.com/precize/enhancer/rcontext"
)

type RelatedResourceProcessor func(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any)

var relatedResourceProcessors = map[string]RelatedResourceProcessor{
	common.AWS_EC2_RESOURCE_TYPE:                         processAWSEC2,
	common.AWS_SUBNET_RESOURCE_TYPE:                      processSubnetAndRouteTable,
	common.AWS_ROUTETABLE_RESOURCE_TYPE:                  processSubnetAndRouteTable,
	common.AWS_EBSSNAPSHOT_RESOURCE_TYPE:                 processAWSEBSSnapshot,
	common.AWS_AMI_RESOURCE_TYPE:                         processAWSAMI,
	common.AWS_ELASTICIP_RESOURCE_TYPE:                   processAWSElasticIP,
	common.AWS_S3_RESOURCE_TYPE:                          processAWSS3,
	common.AWS_SQS_RESOURCE_TYPE:                         processAWSSQS,
	common.AWS_SNS_RESOURCE_TYPE:                         processAWSSNS,
	common.AWS_RDS_RESOURCE_TYPE:                         processAWSRDS,
	common.AWS_RDSSNAPSHOT_RESOURCE_TYPE:                 processAWSRDSSnapshot,
	common.AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE:          processAWSRDSClusterSnapshot,
	common.AWS_AURORA_RESOURCE_TYPE:                      processAWSAurora,
	common.AWS_LAMBDA_RESOURCE_TYPE:                      processAWSLambda,
	common.AWS_APIGATEWAY_RESOURCE_TYPE:                  processAWSAPIGateway,
	common.AWS_CONTAINERIMAGE_RESOURCE_TYPE:              processAWSContainerImage,
	common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE:         processAWSContainerRepository,
	common.AWS_IAM_ROLE_RESOURCE_TYPE:                    processAWSIAMRole,
	common.AWS_DYNAMODB_RESOURCE_TYPE:                    processAWSDynamoDB,
	common.AWS_ATHENA_WORKGROUP_RESOURCE_TYPE:            processAWSAthanaWorkgroup,
	common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE:     processAWSSageMakerEndpointConfig,
	common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE:           processAWSSageMakerEndpoint,
	common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE:        processAWSSageMakerTrainingJob,
	common.AWS_SAGEMAKERMODEL_RESOURCE_TYPE:              processAWSSageMakerModel,
	common.AWS_BEDROCKPROMPT_RESOURCE_TYPE:               processBedrockPromptAndEvalJob,
	common.AWS_BEDROCKEVALJOB_RESOURCE_TYPE:              processBedrockPromptAndEvalJob,
	common.AWS_BEDROCKAGENT_RESOURCE_TYPE:                processBedrockAgent,
	common.AWS_BEDROCKPROVMODELTHROUGHPUT_RESOURCE_TYPE:  processBedrockProvModelThroughput,
	common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE:        processBedrockKnowledgeBase,
	common.AWS_BEDROCKAGENTALIASES_RESOURCE_TYPE:         processBedrockAgentAliases,
	common.AWS_KNOWLEDGEBASEDATASOURCE_RESOURCE_TYPE:     processKnowledgeBaseDataSource,
	common.AWS_NETWORKACL_RESOURCE_TYPE:                  processAWSNetworkACL,
	common.NETWORKINTERFACE_RESOURCE_TYPE:                processNetworkInterface,
	common.AWS_NETWORKINSIGHTSPATH_RESOURCE_TYPE:         processAWSNetworkInsightsPath,
	common.AWS_EKSCLUSTER_RESOURCE_TYPE:                  processAWSEKSCluster,
	common.AWS_ELASTICSEARCH_RESOURCE_TYPE:               processAWSElasticsearch,
	common.AWS_AICOMPREHENDDOCCLASSIFIER_RESOURCE_TYPE:   processAWSComprehendAndSageMakerLabeling,
	common.AWS_SAGEMAKERLABELINGJOBS_RESOURCE_TYPE:       processAWSComprehendAndSageMakerLabeling,
	common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE:           processAWSSageMakerArtifact,
	common.AWS_BEDROCKAGENTGROUP_RESOURCE_TYPE:           processBedrockAgentGroup,
	common.AWS_SSMINSTANCE_RESOURCE_TYPE:                 processAWSSSMInstance,
	common.AWS_GLUECATALOGTABLE_RESOURCE_TYPE:            processAWSGlueCatalogTable,
	common.AWS_GUARDDUTYFINDING_RESOURCE_TYPE:            processAWSGuardDutyFinding,
	common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE:           processAWSSageMakerDomain,
	common.AWS_ELB_RESOURCE_TYPE:                         processAWSELB,
	common.AWS_EBSVOLUME_RESOURCE_TYPE:                   processAWSEBSVolume,
	common.AWS_ECSCLUSTER_RESOURCE_TYPE:                  processAWSECSCluster,
	common.AWS_ECSSERVICE_RESOURCE_TYPE:                  processAWSECSService,
	common.AWS_ECSTASK_RESOURCE_TYPE:                     processAWSECSTask,
	common.AWS_ECSCONTAINERINSTANCE_RESOURCE_TYPE:        processAWSECSContainerInstance,
	common.GCP_INSTANCE_RESOURCE_TYPE:                    processGCPInstance,
	common.GCP_NETWORK_RESOURCE_TYPE:                     processGCPNetwork,
	common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE:           processGCPServiceAccountKey,
	common.GCP_VMDISK_RESOURCE_TYPE:                      processGCPVMDisk,
	common.GCP_DISK_SNAPSHOT_RESOURCE_TYPE:               processGCPDiskSnapshot,
	common.GCP_AUTOSCALER_RESOURCE_TYPE:                  processGCPAutoscaler,
	common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE:        processGCPInstanceGroupManager,
	common.GCP_INSTANCEGROUP_RESOURCE_TYPE:               processGCPInstanceGroup,
	common.GCP_CLOUDSTORAGE_RESOURCE_TYPE:                processGCPCloudStorage,
	common.LOADBALANCER_RESOURCE_TYPE:                    processLoadBalancer,
	common.GCP_BACKENDSERVICE_RESOURCE_TYPE:              processGCPBackendService,
	common.GCP_FUNCTION_RESOURCE_TYPE:                    processGCPFunction,
	common.GCP_DOCKERIMAGE_RESOURCE_TYPE:                 processGCPDockerImage,
	common.GCP_VERTEXAIENDPOINT_RESOURCE_TYPE:            processGCPVertexAIEndpoint,
	common.GCP_DIALOGFLOWAGENT_RESOURCE_TYPE:             processGCPDialogFlowAgent,
	common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE:         processGCPDialogFlowAgentTool,
	common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE:    processGCPDiscoveryEngineDataStore,
	common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:               processGCPBigQueryTable,
	common.GCP_BIGQUERYDATASET_RESOURCE_TYPE:             processGCPBigQueryDataset,
	common.GCP_VERTEXFEATUREVIEW_RESOURCE_TYPE:           processGCPVertexFeatureView,
	common.GCP_VERTEXAIMODEL_RESOURCE_TYPE:               processGCPVertexAIModel,
	common.GCP_VECTORSEARCHINDEXENDPOINT_RESOURCE_TYPE:   processGCPVectorSearchIndexEndpoint,
	common.GCP_NOTEBOOKRUNTIME_RESOURCE_TYPE:             processGCPNotebookRuntime,
	common.GCP_VERTEXAINOTEBOOKINSTANCE_RESOURCE_TYPE:    processGCPVertexAINotebookInstance,
	common.GCP_VERTEXAITENSORBOARD_RESOURCE_TYPE:         processGCPVertexAITensorboard,
	common.GCP_DOCUMENTAIPROCESSOR_RESOURCE_TYPE:         processGCPDocumentAIProcessor,
	common.GCP_GKECLUSTER_RESOURCE_TYPE:                  processGCPGKECluster,
	common.GCP_GKENAMESPACE_RESOURCE_TYPE:                processGCPGKENamespace,
	common.GCP_GKECLUSTERROLE_RESOURCE_TYPE:              processGCPGKEClusterRole,
	common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE:            processGCPGKENamespaceRole,
	common.GCP_GKESERVICEACCOUNT_RESOURCE_TYPE:           processGCPGKEServiceAccount,
	common.GCP_GKEREPLICASET_RESOURCE_TYPE:               processGCPGKEReplicaSet,
	common.GCP_GKEDAEMONSET_RESOURCE_TYPE:                processGCPGKEDaemonSet,
	common.GCP_GKESERVICE_RESOURCE_TYPE:                  processGCPGKEService,
	common.GCP_APPENGINESERVICE_RESOURCE_TYPE:            processGCPAppEngineService,
	common.GCP_APPENGINESERVICEVERSION_RESOURCE_TYPE:     processGCPAppEngineServiceVersion,
	common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE:   processGCPContainerInstanceTemplate,
	common.AZURE_VM_RESOURCE_TYPE:                        processAzureVM,
	common.AZURE_SUBNET_RESOURCE_TYPE:                    processAzureSubnet,
	common.AZURE_VMDISK_RESOURCE_TYPE:                    processAzureVMDisk,
	common.AZURE_SNAPSHOT_RESOURCE_TYPE:                  processAzureSnapshot,
	common.AZURE_NATGATEWAY_RESOURCE_TYPE:                processAzureNATGateway,
	common.AZURE_SQLDB_RESOURCE_TYPE:                     processAzureSQLDB,
	common.AZURE_ARCDATASQLDB_RESOURCE_TYPE:              processAzureArcDataSQLDB,
	common.AZURE_MLWORKSPACE_RESOURCE_TYPE:               processAzureMLWorkspace,
	common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE:      processAzureMLWorkspaceDataStore,
	common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE:         processAzureMLWorkspaceModels,
	common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE:           processAzureMLWorkspaceJobs,
	common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE: processAzureMLWorkspaceComputeMachine,
	common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE:    processAzureMLWorkspaceDeployments,
	common.AZURE_MLWORKSPACEENDPOINTS_RESOURCE_TYPE:      processAzureMLWorkspaceEndpoints,
	common.AZURE_MLHUBCONNECTION_RESOURCE_TYPE:           processAzureMLHubConnection,
	common.AZURE_OPENAIMODEL_RESOURCE_TYPE:               processAzureOpenAIResource,
	common.AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE:          processAzureOpenAIResource,
	common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE:       processAzureOpenAIResource,
	common.AZURE_OPENAICONTENTFILTERBL_RESOURCE_TYPE:     processAzureOpenAIResource,
	common.AZURE_METRICALERT_RESOURCE_TYPE:               processAzureMetricAlert,
	common.AZURE_SCHEDULEDQUERYRULES_RESOURCE_TYPE:       processAzureScheduledQueryRules,
	common.AZURE_AKSCLUSTER_RESOURCE_TYPE:                processAzureAKSCluster,
	common.AZURE_K8NODE_RESOURCE_TYPE:                    processAzureK8Node,
	common.AZURE_COMPUTEIMAGE_RESOURCE_TYPE:              processAzureComputeImage,
	common.AZURE_GRAPHAPP_RESOURCE_TYPE:                  processAzureGraphApp,
	common.OPENAI_VECTORSTOREFILE_RESOURCE_TYPE:          processOpenAIVectorStoreFile,
	common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE:           processOpenAIFineTunedModel,
	common.OPENAI_ASSISTANT_RESOURCE_TYPE:                processOpenAIAssistant,
	common.OPENAI_TUNINGJOB_RESOURCE_TYPE:                processOpenAITuningJob,
	common.OPENAI_TUNINGJOBCHECKPOINT_RESOURCE_TYPE:      processOpenAITuningJobCheckpoint,
	common.OPENAI_APIKEY_RESOURCE_TYPE:                   processOpenAIApiKey,
}

func GetRelatedResourceList(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID, entityJSON string) {
	entityJSONMap := make(map[string]any)
	if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
		return
	}

	if processor, ok := relatedResourceProcessors[resourceContextDoc.ResourceType]; ok {
		processor(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
	} else {
		handleGenericCases(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)
	}
}

type RelatedResourceOpts struct {
	Priority               bool
	Parent                 bool
	TransitiveResourceType string
	Oneway                 bool
	NonContextual          bool
}

func AssignRelatedResource(resourceContext *rcontext.ResourceContext, resourceID, resourceDocID, resourceType, relatedResourceID, relatedResourceDocID, relatedResourceType string, opts ...RelatedResourceOpts) {

	var options RelatedResourceOpts
	if len(opts) > 0 {
		options = opts[0]
	}

	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []rcontext.RelatedResource{}
	}

	relatedResources = append(relatedResources, rcontext.RelatedResource{
		ResourceID:             relatedResourceID,
		ResourceType:           relatedResourceType,
		ResourceDocID:          relatedResourceDocID,
		ContextualRelation:     !options.NonContextual,
		Priority:               options.Priority,
		Parent:                 options.Parent,
		TransitiveResourceType: options.TransitiveResourceType,
	})
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)

	if !options.Oneway {
		reciprocalRelatedResources, exists := resourceContext.GetRelatedResourceList(relatedResourceDocID)
		if !exists {
			reciprocalRelatedResources = []rcontext.RelatedResource{}
		}

		reciprocalRelatedResources = append(reciprocalRelatedResources, rcontext.RelatedResource{
			ResourceID:         resourceID,
			ResourceType:       resourceType,
			ResourceDocID:      resourceDocID,
			ContextualRelation: !options.NonContextual,
			Priority:           options.Priority,
		})
		resourceContext.SetRelatedResourceList(relatedResourceDocID, reciprocalRelatedResources)
	}
}
