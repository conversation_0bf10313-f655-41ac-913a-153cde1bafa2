package resource

import (
	"encoding/json"
	"regexp"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/related"
	"github.com/precize/enhancer/source/attribute"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/enhancer/source/previous"
	"github.com/precize/logger"
)

const (
	MAX_PARENT_THREAD = 10
)

func GetAWSOrgContext(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws org context", []string{resourceContext.TenantID})

	var (
		searchAfter       any
		orgResourcesQuery = `{"_source":["entityId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_ORG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		wg                sync.WaitGroup
		orgResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
	)

	go func() {
		for {
			orgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgResourcesQuery, searchAfter)
			if err != nil {
				close(orgResourcesChan)
				return
			}

			if len(orgResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgResourcesChan <- orgResourcesDocs
			} else {
				close(orgResourcesChan)
				return
			}
		}
	}()

	for orgResourcesDocs := range orgResourcesChan {
		for orgDocID, orgResourcesDoc := range orgResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(orgDocID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processOrg(resourceContext, doc, orgDocID)
			}(orgDocID, orgResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for aws org context", []string{resourceContext.TenantID})
}

func processOrg(resourceContext *rcontext.ResourceContext, orgResourcesDoc map[string]any, orgResourcesDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
	)

	if orgID, ok := orgResourcesDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(orgID, common.AWS_ORG_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         orgID,
			ResourceType:       common.AWS_ORG_RESOURCE_TYPE,
			Account:            orgID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: orgResourcesDocID,
		}

		customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		attribute.GetTagContextOfResource(resourceContext, orgResourcesDoc, &resourceContextInsertDoc)

		if entityJSON, ok := orgResourcesDoc["entityJson"].(string); ok {

			entityJSONMap := make(map[string]any)

			if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if orgEmail, ok := entityJSONMap["email"].(string); ok {
				resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
					context.GetUserContextItem(resourceContext, orgEmail, common.ORG_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
				)
			}

			attribute.GetResourceInbuiltProperty(resourceContext, entityJSONMap, &resourceContextInsertDoc)
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

}

func GetAWSUserAndRoleContext(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws iam user and role context", []string{resourceContext.TenantID})

	var (
		searchAfter       any
		iamResourcesQuery = `{"_source":["entityId","entityType","accountId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.AWS_IAM_USER_RESOURCE_TYPE + `","` + common.AWS_IAM_ROLE_RESOURCE_TYPE + `"]}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}},"size":0,"aggs":{"entityId":{"terms":{"field":"entityId.keyword"}}}}`
		iamResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
		wg                sync.WaitGroup
		batchWg           sync.WaitGroup
		collectedDocIDs   []string
		mutex             sync.Mutex
	)

	go func() {
		for {
			iamResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, iamResourcesQuery, searchAfter)
			if err != nil {
				close(iamResourcesChan)
				return
			}

			if len(iamResourcesDocs) > 0 {
				searchAfter = sortResponse
				iamResourcesChan <- iamResourcesDocs
			} else {
				close(iamResourcesChan)
				return
			}
		}
	}()

	for iamResourcesDocs := range iamResourcesChan {
		for iamDocID, iamDoc := range iamResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processIAMResource(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							ProcessBatchContext(resourceContext, batch)
							previous.ProcessPreviousContext(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(iamDocID, iamDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		ProcessBatchContext(resourceContext, collectedDocIDs)
		previous.ProcessPreviousContext(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for aws iam resource context", []string{resourceContext.TenantID})

}

type AWSIAMRoleEntity struct {
	TrustedEntities []struct {
		Type             string   `json:"type"`
		Identifier       string   `json:"identifier"`
		Actions          []string `json:"actions"`
		TrustedAccountID string   `json:"trustedAccountId"`
	} `json:"trustedEntities"`
}

func processIAMResource(resourceContext *rcontext.ResourceContext, iamResourcesDoc map[string]any, iamResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		uniqueIdentities         = make(map[string]struct{})
	)

	if iamID, ok := iamResourcesDoc["entityId"].(string); ok {
		if accountID, ok := iamResourcesDoc["accountId"].(string); ok {
			if entityType, ok := iamResourcesDoc["entityType"].(string); ok {
				contextDocID = common.GenerateCombinedHashID(iamID, entityType, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         iamID,
					ResourceName:       iamID,
					ResourceType:       entityType,
					Account:            accountID,
					TenantID:           resourceContext.TenantID,
					Region:             "Global",
					ServiceID:          common.AWS_SERVICE_ID_INT,
					CloudResourceDocID: iamResourcesDocID,
				}

				customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
				attribute.GetTagContextOfResource(resourceContext, iamResourcesDoc, &resourceContextInsertDoc)

				entityJSONString, _ := iamResourcesDoc["entityJson"].(string)
				entityJSON := make(map[string]any)
				if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshal", err)
					return
				}

				context.GetTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

				if entityType == common.AWS_IAM_ROLE_RESOURCE_TYPE {

					var awsIAMRoleJSON AWSIAMRoleEntity
					if err := json.Unmarshal([]byte(entityJSONString), &awsIAMRoleJSON); err != nil {
						logger.Print(logger.ERROR, "Failed to unmarshal", err)
						return
					}

					trustedEntities := awsIAMRoleJSON.TrustedEntities

					for _, trustedEntity := range trustedEntities {
						for identifierType, format := range map[string]string{
							"user":    `^arn:aws:iam::\d{12}:user/(.+)$`,
							"role":    `^arn:aws:iam::\d{12}:role/(.+)$`,
							"account": `^\d{12}$`} {

							r := regexp.MustCompile(format)
							if matches := r.FindStringSubmatch(trustedEntity.Identifier); len(matches) > 0 {

								switch identifierType {

								case "user":
									if len(matches) == 3 {
										userAccount := matches[1]
										user := matches[2]
										activity.GetAWSIAMEntityOwnersForPolicy(resourceContext, user, userAccount, common.AWS_IAM_USER_RESOURCE_TYPE, &resourceContextInsertDoc,
											uniqueIdentities, "User owned IAM User "+user+" has been added as a trusted entity for the resource", 1)
									}
								case "role":
									if len(matches) == 3 {
										roleAccount := matches[1]
										role := matches[2]
										activity.GetAWSIAMEntityOwnersForPolicy(resourceContext, role, roleAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, &resourceContextInsertDoc,
											uniqueIdentities, "User owned IAM Role "+role+" has been added as a trusted entity for the resource", 1)
									}
								case "account":

									accContextDocID := common.GenerateCombinedHashID(trustedEntity.Identifier, common.AWS_ACCOUNT_RESOURCE_TYPE, trustedEntity.Identifier, resourceContext.LastCollectedAt, resourceContext.TenantID)
									origin := "Internal"
									if _, ok := resourceContext.GetResourceContextInsertDoc(accContextDocID); !ok {
										origin = "External"
									}

									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, context.GetUserContextItem(resourceContext, trustedEntity.Identifier+contextutils.ACCOUNT_USER_SUFFIX, common.POLICYBINDING_USER_TYPE, origin+" AWS account has been added as trusted entity for the resource", "", resourceContextInsertDoc.Account, nil))
								}
							}
						}
					}
				}

				accContextID := common.GenerateCombinedHashID(accountID, common.AWS_ACCOUNT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if accRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(accContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(accRscCtxInsertDoc, common.AWS_ACCOUNT_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(accRscCtxInsertDoc)
					resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners, accRscCtxInsertDoc.CostOwners...)
					resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners, accRscCtxInsertDoc.SecurityOwners...)
					resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners, accRscCtxInsertDoc.OpsOwners...)
				}

				if envName := context.GetEnvironmentNameFromValue(iamID); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RESOURCE_NAME_ENV_TYPE,
							Desc: context.GetStaticDescriptionOfEnvType(common.RESOURCE_NAME_ENV_TYPE),
						},
					)
				}

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

			}
		}
	}

	return
}

func GetOrgUnitContext(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws org unit context", []string{resourceContext.TenantID})

	var (
		searchAfter           any
		orgUnitResourcesQuery = `{"_source":["entityId","tags","accountId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"entityType.keyword":"` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		orgUnitResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		for {
			orgUnitResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgUnitResourcesQuery, searchAfter)
			if err != nil {
				close(orgUnitResourcesChan)
				return
			}

			if len(orgUnitResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgUnitResourcesChan <- orgUnitResourcesDocs
			} else {
				close(orgUnitResourcesChan)
				return
			}
		}
	}()

	for orgUnitResourcesDocs := range orgUnitResourcesChan {
		for orgUnitDocID, orgUnitDoc := range orgUnitResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processOrgUnitResource(resourceContext, docID, doc)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							ProcessBatchContext(resourceContext, batch)
							previous.ProcessPreviousContext(resourceContext, batch)
							context.ProcessResourceNamesForTeams(resourceContext, batch)
							context.ProcessResourceNamesForApps(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(orgUnitDocID, orgUnitDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		ProcessBatchContext(resourceContext, collectedDocIDs)
		previous.ProcessPreviousContext(resourceContext, collectedDocIDs)
		context.ProcessResourceNamesForTeams(resourceContext, collectedDocIDs)
		context.ProcessResourceNamesForApps(resourceContext, collectedDocIDs)
	}

	// Have to iterate again because parent of org unit can be another org unit
	searchAfter = nil
	orgUnitResourcesChan = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
	semaphore = make(chan struct{}, MAX_PARENT_THREAD)

	go func() {
		for {
			orgUnitResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgUnitResourcesQuery, searchAfter)
			if err != nil {
				close(orgUnitResourcesChan)
				return
			}

			if len(orgUnitResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgUnitResourcesChan <- orgUnitResourcesDocs
			} else {
				close(orgUnitResourcesChan)
				return
			}
		}
	}()

	for orgUnitResourcesDocs := range orgUnitResourcesChan {
		for _, orgUnitResourcesDoc := range orgUnitResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processOrgUnitForRelatedResource(resourceContext, doc)
			}(orgUnitResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for aws org unit context", []string{resourceContext.TenantID})
}

func processOrgUnitResource(resourceContext *rcontext.ResourceContext, orgUnitResourcesDocID string, orgUnitResourcesDoc map[string]any) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		orgUnitName              string
	)

	if orgUnitID, ok := orgUnitResourcesDoc["entityId"].(string); ok {

		orgOrOrgUnitID, _ := orgUnitResourcesDoc["accountId"].(string)

		entityJSONString, _ := orgUnitResourcesDoc["entityJson"].(string)
		entityJSON := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err == nil {
			orgUnitName = GetNameForAWSResource(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, "", entityJSONString)
		}

		contextDocID = common.GenerateCombinedHashID(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         orgUnitID,
			ResourceName:       orgUnitName,
			ResourceType:       common.AWS_ORGUNIT_RESOURCE_TYPE,
			Account:            orgOrOrgUnitID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: orgUnitResourcesDocID,
		}

		customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		attribute.GetResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
		attribute.GetTagContextOfResource(resourceContext, orgUnitResourcesDoc, &resourceContextInsertDoc)
		context.GetTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

		//TODO: Get owner from updated by in entity json

		if envName := context.GetEnvironmentNameFromValue(orgUnitName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.ORGUNIT_NAME_ENV_TYPE,
					Desc: context.GetStaticDescriptionOfEnvType(common.ORGUNIT_NAME_ENV_TYPE),
				},
			)

			context.IncrementParentChildEnvCount(resourceContext, envName, orgOrOrgUnitID, "")
		}

		if team := context.GetTeamNameFromValue(orgUnitName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.ORGUNIT_NAME_TEAM_TYPE,
					Desc: context.GetStaticDescriptionOfTeamType(common.ORGUNIT_NAME_TEAM_TYPE),
				},
			)
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func processOrgUnitForRelatedResource(resourceContext *rcontext.ResourceContext, orgUnitDoc map[string]any) {

	if orgUnitID, ok := orgUnitDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		orgOrOrgUnitID, _ := orgUnitDoc["accountId"].(string)

		if len(orgOrOrgUnitID) > 0 {

			orgUnitContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgUnitContextID); ok {

				if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
					tmp.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORGUNIT_RESOURCE_TYPE)
					tmp.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(orgRscCtxInsertDoc)
					tmp.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(orgRscCtxInsertDoc)
					resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

					related.AssignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType, orgOrOrgUnitID, orgUnitContextID, common.AWS_ORGUNIT_RESOURCE_TYPE, related.RelatedResourceOpts{NonContextual: true})
				}

			} else {

				orgContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORG_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {

					if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
						tmp.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORG_RESOURCE_TYPE)
						tmp.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(orgRscCtxInsertDoc)
						resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

						related.AssignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType, orgOrOrgUnitID, orgContextID, common.AWS_ORG_RESOURCE_TYPE, related.RelatedResourceOpts{NonContextual: true})

					}
				}
			}
		}
	}

}

func GetAccountContext(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws account context", []string{resourceContext.TenantID})

	var (
		searchAfter           any
		accountResourcesQuery = `{"_source":["entityId","entityJson","tags","accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_ACCOUNT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		accountResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		for {
			accountResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, accountResourcesQuery, searchAfter)
			if err != nil {
				close(accountResourcesChan)
				return
			}

			if len(accountResourcesDocs) > 0 {
				searchAfter = sortResponse
				accountResourcesChan <- accountResourcesDocs
			} else {
				close(accountResourcesChan)
				return
			}
		}
	}()

	for accountResourcesDocs := range accountResourcesChan {
		for accountDocID, accountDoc := range accountResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processAccount(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							ProcessBatchContext(resourceContext, batch)
							previous.ProcessPreviousContext(resourceContext, batch)
							context.ProcessResourceNamesForTeams(resourceContext, batch)
							context.ProcessResourceNamesForApps(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(accountDocID, accountDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		ProcessBatchContext(resourceContext, collectedDocIDs)
		previous.ProcessPreviousContext(resourceContext, collectedDocIDs)
		context.ProcessResourceNamesForTeams(resourceContext, collectedDocIDs)
		context.ProcessResourceNamesForApps(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for aws account context", []string{resourceContext.TenantID})
}

func processAccount(resourceContext *rcontext.ResourceContext, accountResourcesDoc map[string]any, accountResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		accountName              string
		entityJSONMap            = make(map[string]any)
	)

	if accountID, ok := accountResourcesDoc["entityId"].(string); ok {

		orgOrOrgUnitID, _ := accountResourcesDoc["accountId"].(string)

		if entityJSON, ok := accountResourcesDoc["entityJson"].(string); ok {
			accountName = GetNameForAWSAccount(entityJSON)
			if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}
		}

		contextDocID = common.GenerateCombinedHashID(accountID, common.AWS_ACCOUNT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         accountID,
			ResourceName:       accountName,
			ResourceType:       common.AWS_ACCOUNT_RESOURCE_TYPE,
			Account:            orgOrOrgUnitID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: accountResourcesDocID,
		}

		customer.SetCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		attribute.GetResourceInbuiltProperty(resourceContext, entityJSONMap, &resourceContextInsertDoc)
		attribute.GetTagContextOfResource(resourceContext, accountResourcesDoc, &resourceContextInsertDoc)
		context.GetTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSONMap)

		// If org is not onboarded, accountId is self
		if orgOrOrgUnitID != accountID {

			// Account parent can be either org unit or org

			orgUnitContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if orgUnitRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgUnitContextID); ok {
				resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(orgUnitRscCtxInsertDoc, common.AWS_ORGUNIT_RESOURCE_TYPE)
				resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(orgUnitRscCtxInsertDoc)
				resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = context.GetInheritedTeam(orgUnitRscCtxInsertDoc)

				related.AssignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType, orgOrOrgUnitID, orgUnitContextID, common.AWS_ORGUNIT_RESOURCE_TYPE, related.RelatedResourceOpts{NonContextual: true})
			} else {

				orgContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORG_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = context.GetInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORG_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = context.GetInheritedEnv(orgRscCtxInsertDoc)

					related.AssignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType, orgOrOrgUnitID, orgContextID, common.AWS_ORG_RESOURCE_TYPE, related.RelatedResourceOpts{NonContextual: true})
				}
			}
		}

		if envName := context.GetEnvironmentNameFromValue(accountName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.ACCOUNT_NAME_ENV_TYPE,
					Desc: context.GetStaticDescriptionOfEnvType(common.ACCOUNT_NAME_ENV_TYPE),
				},
			)

			context.IncrementParentChildEnvCount(resourceContext, envName, orgOrOrgUnitID, "")
		}

		if team := context.GetTeamNameFromValue(accountName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.ACCOUNT_NAME_TEAM_TYPE,
					Desc: context.GetStaticDescriptionOfTeamType(common.ACCOUNT_NAME_TEAM_TYPE),
				},
			)
		}

		if accountEmail, ok := entityJSONMap["email"].(string); ok {
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
				context.GetUserContextItem(resourceContext, accountEmail, common.ACCOUNT_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
			)
		}

		if contactInterfaceArray, ok := entityJSONMap["alternateContacts"].([]any); ok {

			for _, contactInterface := range contactInterfaceArray {

				if contactMap, ok := contactInterface.(map[string]any); ok {

					if ownerType, ok := contactMap["alternateContactType"].(string); ok {

						if username, ok := contactMap["emailAddress"].(string); ok {

							userType := common.ACCOUNT_CONTACT_USER_TYPE

							switch ownerType {

							case "BILLING":

								resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners,
									context.GetUserContextItem(resourceContext, username, userType, "", "", resourceContextInsertDoc.Account, nil),
								)

							case "OPERATIONS":

								resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
									context.GetUserContextItem(resourceContext, username, userType, "", "", resourceContextInsertDoc.Account, nil),
								)

							case "SECURITY":

								resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners,
									context.GetUserContextItem(resourceContext, username, userType, "", "", resourceContextInsertDoc.Account, nil),
								)
							}
						}
					}
				}
			}
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

	}

	return
}

func GetSecurityGroupRules(resourceContext *rcontext.ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws security group rules", []string{resourceContext.TenantID})

	var (
		searchAfter      any
		sgResourcesQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_SG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		sgResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore        = make(chan struct{}, MAX_PARENT_THREAD)
		wg               sync.WaitGroup
	)

	go func() {
		for {
			sgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, sgResourcesQuery, searchAfter)
			if err != nil {
				close(sgResourcesChan)
				return
			}

			if len(sgResourcesDocs) > 0 {
				searchAfter = sortResponse
				sgResourcesChan <- sgResourcesDocs
			} else {
				close(sgResourcesChan)
				return
			}
		}
	}()

	for sgResourcesDocs := range sgResourcesChan {
		for sgDocID, sgDoc := range sgResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processSGResource(resourceContext, doc)
			}(sgDocID, sgDoc)
		}
	}

	wg.Wait()
	logger.Print(logger.INFO, "Processing complete for aws security group rules", []string{resourceContext.TenantID})
}

func processSGResource(resourceContext *rcontext.ResourceContext, sgResourcesDoc map[string]any) {
	var networkInboundPorts = make(map[int]struct{})

	if sgID, ok := sgResourcesDoc["entityId"].(string); ok {
		if entityJson, ok := sgResourcesDoc["entityJson"].(string); ok {
			entityJsonMap := make(map[string]any)

			if err := json.Unmarshal([]byte(entityJson), &entityJsonMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if inboundRules, ok := entityJsonMap["inboundRules"].([]any); ok {
				for _, inboundRule := range inboundRules {
					if inboundRuleJson, ok := inboundRule.(map[string]any); ok {
						fromPort, _ := inboundRuleJson["fromPort"].(float64)
						toPort, _ := inboundRuleJson["toPort"].(float64)

						networkInboundPorts[int(fromPort)] = struct{}{}
						networkInboundPorts[int(toPort)] = struct{}{}
					}
				}
			}
		}

		resourceContext.SetNetworkInboundPorts(strings.ToLower(sgID), networkInboundPorts)
	}
}
